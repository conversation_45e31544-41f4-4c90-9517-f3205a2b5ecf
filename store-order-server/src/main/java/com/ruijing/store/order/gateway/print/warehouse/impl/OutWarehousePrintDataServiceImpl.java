package com.ruijing.store.order.gateway.print.warehouse.impl;

import com.google.common.collect.Lists;
import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.shop.category.api.dto.CategoryDTO;
import com.ruijing.shop.category.api.enums.InboundTypeEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderDetailExtraEnum;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.print.dto.warehouse.OutWarehousePrintDataDTO;
import com.ruijing.store.order.gateway.print.dto.warehouse.WarehouseProductPrintDataDTO;
import com.ruijing.store.order.gateway.print.service.impl.GetPrintCommonDataService;
import com.ruijing.store.order.gateway.print.util.WarehouseDataTranslator;
import com.ruijing.store.order.gateway.print.warehouse.OutWarehousePrintDataService;
import com.ruijing.store.order.rpc.client.BizExitServiceClient;
import com.ruijing.store.order.rpc.client.CategoryServiceClient;
import com.ruijing.store.order.rpc.client.OrderDetailExtraClient;
import com.ruijing.store.warehouse.message.constant.WarehouseConstant;
import com.ruijing.store.warehouse.utils.CategoryUtil;
import com.ruijing.store.warehouse.utils.PriceUtil;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDetailDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2023/3/1 18:39
 * @description
 */
@Service
public class OutWarehousePrintDataServiceImpl implements OutWarehousePrintDataService {

    /**
     * 需要打印出库单条形码的单位code
     */
    private static final List<String> ORG_CODE_NEED_EXIT_NO_BARCODE = Lists.newArrayList(OrgEnum.GUANG_DONG_YAO_KE_DA_XUE.getCode());

    @Resource
    private GetPrintCommonDataService getPrintCommonDataService;

    @Resource
    private CategoryServiceClient categoryServiceClient;

    @Resource
    private BizExitServiceClient bizExitServiceClient;

    @Resource
    private OrderDetailExtraClient orderDetailExtraClient;

    @Override
    public List<OutWarehousePrintDataDTO> getOutWarehousePrintData(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList, List<BizWarehouseEntryDTO> warehouseApplicationInfoList){
        // 根据订单号查找出库单列表, 并去掉没有入库单的的
        List<BizWarehouseExitDTO> outWarehouseApplicationInfoList = bizExitServiceClient.queryExitByOrderNo(orderMasterDO.getForderno());
        List<Integer> validExitIdList = warehouseApplicationInfoList.stream().map(BizWarehouseEntryDTO::getExitId).collect(toList());
        if (CollectionUtils.isNotEmpty(outWarehouseApplicationInfoList)) {
            outWarehouseApplicationInfoList = outWarehouseApplicationInfoList.stream().filter(s -> validExitIdList.contains(s.getId())).collect(toList());
        }
                
        List<OutWarehousePrintDataDTO> outWarehouseApplicationDetailVOList = new ArrayList<>();
        for (BizWarehouseExitDTO bizWarehouseExitDTO : outWarehouseApplicationInfoList) {
            //出库单状态（状态0未出库，1已出库）
            if (bizWarehouseExitDTO.getStatus() == 1) {
                OutWarehousePrintDataDTO outWarehouseApplicationDetailVO = this.constructOutWareHouseData(orderMasterDO.getFusercode(), bizWarehouseExitDTO, orderDetailDOList);
                outWarehouseApplicationDetailVOList.add(outWarehouseApplicationDetailVO);
            }
        }
        return outWarehouseApplicationDetailVOList;
    }
    
    private OutWarehousePrintDataDTO constructOutWareHouseData(String orgCode, BizWarehouseExitDTO outWarehouseApplicationInfo, List<OrderDetailDO> orderDetailDOList){
        //出库单信息
        OutWarehousePrintDataDTO outData = new OutWarehousePrintDataDTO();
        //封装入库单信息
        outData.setOutWarehouseApplicationNo(outWarehouseApplicationInfo.getExitNo());
        outData.setOutWarehouseApplicationId(outWarehouseApplicationInfo.getId());
        outData.setDepartmentName(outWarehouseApplicationInfo.getDeptName());
        outData.setStatus(outWarehouseApplicationInfo.getStatus());
        outData.setStatusName(outWarehouseApplicationInfo.getStatus() == null ? null : getStatusName(outWarehouseApplicationInfo.getStatus()));
        outData.setOutWarehouseApplicant(outWarehouseApplicationInfo.getUserName());
        outData.setOutWarehouseApplicationTime(outWarehouseApplicationInfo.getCreateTime() == null ? null : outWarehouseApplicationInfo.getCreateTime().getTime());
        outData.setWarehouseId(outWarehouseApplicationInfo.getRoomId());
        outData.setWarehouseName(outWarehouseApplicationInfo.getRoomName());
        Date outWarehouseTime = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, outWarehouseApplicationInfo.getExitTime());
        outData.setOutWarehouseTime(outWarehouseTime == null ? null : outWarehouseTime.getTime());
        //获取入库单号对应条形码
        if(ORG_CODE_NEED_EXIT_NO_BARCODE.contains(orgCode)){
            outData.setExitNoBarcode(getPrintCommonDataService.getBarCode(outWarehouseApplicationInfo.getExitNo()));    
        }
        List<WarehouseProductPrintDataDTO> warehouseProductInfoVOList = this.constructOutWarehouseProduct(outWarehouseApplicationInfo.getExitDetailDTOList(), orderDetailDOList, orgCode);
        //提取申请单相关商品信息
        outData.setWarehouseProductInfoVOList(warehouseProductInfoVOList);
        String totalPrice = outData.getWarehouseProductInfoVOList().stream().map(product -> new BigDecimal(product.getTotalPrice())).reduce(BigDecimal.ZERO, BigDecimal::add).toString();
        outData.setTotalPrice(totalPrice);
        outData.setTotalPriceInChinese(PriceUtil.convert(totalPrice));
        // 计算出库单商品数量总和
        int totalProductQuantity = 0;
        if (CollectionUtils.isNotEmpty(outData.getWarehouseProductInfoVOList())) {
            totalProductQuantity = outData.getWarehouseProductInfoVOList().stream()
                    .filter(Objects::nonNull)
                    .mapToInt(product -> Objects.nonNull(product.getQuantity()) ? product.getQuantity() : 0)
                    .sum();
        }
        outData.setTotalProductQuantity(totalProductQuantity);
        // 设置顶级分类金额字段
        setTopCategoryAmount(outData);
        return outData;
    }

    /**
     * 设置顶级分类金额字段
     * 
     * @param outData 出库打印数据
     */
    private void setTopCategoryAmount(OutWarehousePrintDataDTO outData) {
        if (Objects.isNull(outData) || CollectionUtils.isEmpty(outData.getWarehouseProductInfoVOList())) {
            return;
        }

        // 获取所有一级分类ID
        List<Integer> firstLevelCategoryIds = outData.getWarehouseProductInfoVOList().stream()
                .map(WarehouseProductPrintDataDTO::getFirstLevelCategoryId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(firstLevelCategoryIds)) {
            return;
        }

        // 获取分类信息
        List<Long> categoryLongIds = firstLevelCategoryIds.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<CategoryDTO> categoryDTOList = categoryServiceClient.batchLoadSelf(categoryLongIds);

        if (CollectionUtils.isEmpty(categoryDTOList)) {
            return;
        }

        // 构建分类ID与分类信息的映射
        Map<Integer, CategoryDTO> categoryMap = categoryDTOList.stream()
                .collect(Collectors.toMap(c -> c.getId().intValue(), Function.identity(), (o, n) -> n));

        // 按inboundType分类计算金额
        BigDecimal reagentTotalPrice = BigDecimal.ZERO;
        BigDecimal consumablesAndAnimalTotalPrice = BigDecimal.ZERO;

        for (WarehouseProductPrintDataDTO product : outData.getWarehouseProductInfoVOList()) {
            if (Objects.isNull(product.getFirstLevelCategoryId()) || StringUtils.isBlank(product.getTotalPrice())) {
                continue;
            }

            CategoryDTO categoryDTO = categoryMap.get(product.getFirstLevelCategoryId());
            if (Objects.isNull(categoryDTO) || Objects.isNull(categoryDTO.getInboundType())) {
                continue;
            }

            BigDecimal productPrice = new BigDecimal(product.getTotalPrice());

            // 试剂类型
            if (Objects.equals(categoryDTO.getInboundType(), InboundTypeEnum.REAGENT.getValue())) {
                reagentTotalPrice = reagentTotalPrice.add(productPrice);
            } else if (Objects.equals(categoryDTO.getInboundType(), InboundTypeEnum.CONSUMABLES.getValue())
                    || Objects.equals(categoryDTO.getInboundType(), InboundTypeEnum.ANIMAL.getValue())) {
                // 耗材、动物类型
                consumablesAndAnimalTotalPrice = consumablesAndAnimalTotalPrice.add(productPrice);
            }
        }

        outData.setReagentTotalPrice(reagentTotalPrice.setScale(2, RoundingMode.HALF_UP).toString());
        outData.setConsumablesAndAnimalTotalPrice(consumablesAndAnimalTotalPrice.setScale(2, RoundingMode.HALF_UP).toString());
    }

    private static String getStatusName(int status) {
        switch (status) {
            case 0:
                return "未出库";
            case 1:
                return "已出库";
            default:
                return "未知状态";
        }
    }
    

    /**
     * 出库商品数据装填
     * @param entryDetails 出库商品数据
     * @param orderDetailDOList 订单商品数据
     * @param orgCode 机构代码
     * @return 入库商品数据
     */
    private List<WarehouseProductPrintDataDTO> constructOutWarehouseProduct(List<BizWarehouseExitDetailDTO> entryDetails, List<OrderDetailDO> orderDetailDOList, String orgCode) {
        // 映射为商品详情id和货号的map，用于补充数据
        Map<String, OrderDetailDO> goodCodeAndOrderDetailMap = orderDetailDOList.stream().collect(Collectors.toMap(o -> o.getFgoodcode().trim(), Function.identity(), (o, n) -> n));
        Map<Integer, OrderDetailDO> detailIdAndOrderDetailMap = orderDetailDOList.stream().collect(Collectors.toMap(OrderDetailDO::getId, Function.identity(), (o, n) -> n));
        List<WarehouseProductPrintDataDTO> warehouseProductPrintDataDTOList = new ArrayList<>();
        // 这里暂存detailID和结果映射，根据批次划分用到
        Map<WarehouseProductPrintDataDTO, Integer> result2DetailId = New.map();
        for (BizWarehouseExitDetailDTO entryDetail : entryDetails) {
            Preconditions.notNull(entryDetail.getProductCode(), "出库商品的货号为空：" + entryDetail.getId());
            OrderDetailDO matchDetail;
            // 优先detailId,旧数据没有的用货号来匹配
            if (entryDetail.getOrderDetailId() != null) {
                matchDetail = detailIdAndOrderDetailMap.get(entryDetail.getOrderDetailId());
            } else {
                matchDetail = goodCodeAndOrderDetailMap.get(entryDetail.getProductCode().trim());
            }
            BusinessErrUtil.notNull(matchDetail, ExecptionMessageEnum.OUTBOUND_ORDER_ITEM_DETAILS_NOT_FOUND, entryDetail.getProductCode().trim(), entryDetail.getOrderDetailId());
            WarehouseProductPrintDataDTO warehouseProductPrintDataDTO = WarehouseDataTranslator.outWarehouseProductDto2PrintDTO(entryDetail);
            //商品单价
            BigDecimal singleProductPrice = entryDetail.getUnitPrice() != null && entryDetail.getUnitPrice().compareTo(BigDecimal.ZERO) > 0 ? entryDetail.getUnitPrice() : matchDetail.getFbidprice();
            warehouseProductPrintDataDTO.setSinglePrice(singleProductPrice.toString());
            //商品总价
            BigDecimal singleProductTotalPrice = entryDetail.getPrice() != null && entryDetail.getPrice().compareTo(BigDecimal.ZERO) > 0 ?
                    entryDetail.getPrice() : singleProductPrice.multiply(BigDecimal.valueOf(entryDetail.getExitedNum()));
            warehouseProductPrintDataDTO.setTotalPrice(singleProductTotalPrice.toString());

            warehouseProductPrintDataDTO.setCategoryId(matchDetail.getCategoryid());
            warehouseProductPrintDataDTO.setProductId(matchDetail.getProductSn());
            warehouseProductPrintDataDTO.setCategoryTag(matchDetail.getCategoryTag());
            warehouseProductPrintDataDTO.setFirstLevelCategoryId(matchDetail.getFirstCategoryId());
            warehouseProductPrintDataDTO.setOrderDetailId(matchDetail.getId());
            warehouseProductPrintDataDTOList.add(warehouseProductPrintDataDTO);
            result2DetailId.put(warehouseProductPrintDataDTO, entryDetail.getOrderDetailId());
        }

        if (WarehouseConstant.ORG_CODE_NEED_FIRST_LEVEL_CATEGORY.contains(orgCode)) {
            //获取商品一级分类(个性化)
            List<Integer> categoryIds = warehouseProductPrintDataDTOList.stream().map(WarehouseProductPrintDataDTO::getCategoryId).distinct().collect(Collectors.toList());
            List<CategoryDTO> categoryDTOList = categoryServiceClient.getAllCategoryByIds(categoryIds);
            BusinessErrUtil.notEmpty(categoryDTOList, ExecptionMessageEnum.FAILED_TO_OBTAIN_PRODUCT_CATEGORY_INFO);
            warehouseProductPrintDataDTOList.forEach(product -> {
                CategoryDTO categoryDTO = CategoryUtil.getFirstLevelCategory(product.getCategoryId(), categoryDTOList);
                BusinessErrUtil.notNull(categoryDTO, ExecptionMessageEnum.PRIMARY_CATEGORY_INFO_NOT_FOUND, product.getCategoryId());
                product.setFirstLevelCategoryName(categoryDTO.getName());
            });
        }

        // 填充订单详情扩展信息
        fillOrderDetailExtraInfo(warehouseProductPrintDataDTOList);

        List<WarehouseProductPrintDataDTO> secondResult = New.list();
        warehouseProductPrintDataDTOList.forEach(warehouseDTO -> {
            Integer detailId = result2DetailId.get(warehouseDTO);
            warehouseDTO.setOrderDetailId(detailId);
        });

        return CollectionUtils.isNotEmpty(secondResult) ? secondResult : warehouseProductPrintDataDTOList;
    }

    /**
     * 填充订单详情扩展信息
     *
     * @param warehouseProductPrintDataDTOList 商品打印数据列表
     */
    private void fillOrderDetailExtraInfo(List<WarehouseProductPrintDataDTO> warehouseProductPrintDataDTOList) {
        if (CollectionUtils.isEmpty(warehouseProductPrintDataDTOList)) {
            return;
        }

        // 获取所有需要查询扩展信息的订单详情ID
        List<Integer> orderDetailIds = warehouseProductPrintDataDTOList.stream()
                .map(WarehouseProductPrintDataDTO::getOrderDetailId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderDetailIds)) {
            return;
        }

        // 查询订单详情扩展信息
        List<OrderDetailExtraDTO> orderDetailExtraDTOList = orderDetailExtraClient.listOrderDetailExtra(null, orderDetailIds);
        if (CollectionUtils.isEmpty(orderDetailExtraDTOList)) {
            return;
        }

        // 按扩展类型分组
        Map<Integer, Map<Integer, String>> orderDetailId2ExtraInfoMap = orderDetailExtraDTOList.stream()
                .collect(Collectors.groupingBy(
                        OrderDetailExtraDTO::getOrderDetailId,
                        Collectors.toMap(
                                OrderDetailExtraDTO::getExtraKeyType,
                                OrderDetailExtraDTO::getExtraValue,
                                (v1, v2) -> v1
                        )
                ));

        // 填充扩展信息到商品数据中
        warehouseProductPrintDataDTOList.forEach(product -> {
            if (Objects.nonNull(product.getOrderDetailId())) {
                Map<Integer, String> extraInfoMap = orderDetailId2ExtraInfoMap.get(product.getOrderDetailId());
                if (Objects.nonNull(extraInfoMap)) {
                    // 填充医疗器械注册证书编号
                    String medicalDeviceRegisCertNumber = extraInfoMap.get(OrderDetailExtraEnum.MEDICAL_DEVICE_REGIS_CERT_NUMBER.getType());
                    product.setMedicalDeviceRegisCertNumber(medicalDeviceRegisCertNumber);
                }
            }
        });
    }
}
