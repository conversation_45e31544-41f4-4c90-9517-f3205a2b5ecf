package com.ruijing.store.order.business.service.impl;

import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.util.StrUtil;
import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderDetailAcceptanceFileDTO;
import com.reagent.order.base.order.dto.OrderDetailAcceptancePicDTO;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.reagent.order.dto.config.OldDateConfigDTO;
import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.dto.request.OrderEventStatusRequestDTO;
import com.reagent.order.dto.response.OrderEventStatusResponseDTO;
import com.reagent.order.enums.OrderEventStatusEnum;
import com.reagent.order.enums.OrderPushEventEnum;
import com.reagent.order.enums.config.OmsDockingConfigValueEnum;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardManagerDTO;
import com.reagent.research.fundcard.enums.FundTypeEnum;
import com.reagent.research.fundcard.enums.SpeciesEnum;
import com.reagent.research.statement.api.enums.StatementStatusEnum;
import com.reagent.research.statement.api.invoice.dto.InvoiceDTO;
import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.ruijing.base.address.api.dto.NewDeliveryAddressLabelDTO;
import com.ruijing.base.address.api.enums.RegionTypeEnum;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.base.store.auth.api.constant.BuyerCenterAccessConstant;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.order.saturn.api.accept.approve.dto.AcceptApproveInfoDTO;
import com.ruijing.order.saturn.api.accept.approve.enums.AcceptApprovalStatusEnum;
import com.ruijing.order.saturn.api.audit.sampling.dto.OrderAuditSamplingDTO;
import com.ruijing.order.saturn.api.contract.dto.OrderContractQueryDTO;
import com.ruijing.order.saturn.api.contract.vo.OrderContractInfoVO;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.order.utils.LocaleUtils;
import com.ruijing.order.whitehole.database.dto.file.OrderUploadFileDTO;
import com.ruijing.order.whitehole.eventbase.enums.file.FileBusinessTypeEnum;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.search.client.enums.SortOrder;
import com.ruijing.search.client.filter.OrFilter;
import com.ruijing.search.client.filter.TermFilter;
import com.ruijing.search.client.request.Request;
import com.ruijing.search.client.sort.FieldSortItem;
import com.ruijing.shop.carryfee.api.dto.BizSearchProductCarryFeeDTO;
import com.ruijing.shop.carryfee.api.dto.BizSearchProductDetailCarryFeeDTO;
import com.ruijing.shop.category.api.enums.RegulatoryTypeEnum;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.shop.goods.api.enums.StockTypeEnum;
import com.ruijing.shop.trade.api.dto.OrderDeliveryInfoDTO;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.approval.api.dto.PurchaseApprovalLogDTO;
import com.ruijing.store.approval.api.enums.ApprovalDockingTypeEnum;
import com.ruijing.store.approval.api.enums.FlowRoleAccessEnum;
import com.ruijing.store.baseconfig.api.dto.typeConfig.orderContract.OrderContractConfig;
import com.ruijing.store.baseconfig.api.dto.typeConfig.orderContract.enums.OrderContractUploadConditionEnum;
import com.ruijing.store.baseconfig.api.dto.typeConfig.orderContract.enums.OrderUploadContractEnum;
import com.ruijing.store.baseconfig.api.enums.ConfigCodeEnum;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.electronicsign.api.dto.ElectronicSignOperationDTO;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.docking.enums.DockingPushStatusEnum;
import com.ruijing.store.order.api.base.docking.enums.DockingTypeEnum;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderDetailExtraEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.*;
import com.ruijing.store.order.api.base.other.dto.OrderBankDataDTO;
import com.ruijing.store.order.api.enums.OrderInventoryStatusEnum;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.general.dto.FundCardSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderLogSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.*;
import com.ruijing.store.order.base.baseconfig.enums.TimeOutConfigType;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.enums.TimeOutEnums;
import com.ruijing.store.order.base.core.mapper.*;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.*;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.base.minor.mapper.OrderConfirmForTheRecordDOMapper;
import com.ruijing.store.order.base.minor.mapper.OrderContractMapper;
import com.ruijing.store.order.base.minor.mapper.OrderPicMapper;
import com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO;
import com.ruijing.store.order.base.minor.model.OrderContract;
import com.ruijing.store.order.base.minor.model.OrderPic;
import com.ruijing.store.order.base.timeoutstatistics.mapper.TimeoutStatisticsMapper;
import com.ruijing.store.order.base.timeoutstatistics.model.TimeoutStatisticsDO;
import com.ruijing.store.order.base.util.FundCardUtils;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.*;
import com.ruijing.store.order.business.enums.myorderlist.*;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.handler.OrderMessageHandler;
import com.ruijing.store.order.business.service.*;
import com.ruijing.store.order.business.service.constant.CustomAcceptorConstant;
import com.ruijing.store.order.business.service.orgondemand.SYSUOrderService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.*;
import com.ruijing.store.order.gateway.buyercenter.request.ModifyAddrRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.ModifyOrderExtInfoRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.OrderPushFailItemDTO;
import com.ruijing.store.order.gateway.buyercenter.request.goodsreturn.GoodsReturnBriefInfoRequest;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderStatusLimitDaysRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.ElectronicSignConfigVO;
import com.ruijing.store.order.gateway.buyercenter.vo.ElectronicSignInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn.GoodsReturnBriefInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn.GoodsReturnProductBriefVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderAddressInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderFundcardVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderLogisticsInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderOfflineInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.*;
import com.ruijing.store.order.other.translator.FileTranslator;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.service.OldDateService;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.LocalI18nUtils;
import com.ruijing.store.rule.dto.PurchaseRuleRequestDTO;
import com.ruijing.store.rule.dto.PurchaseRuleResultDTO;
import com.ruijing.store.rule.dto.RuleProductVerificationDTO;
import com.ruijing.store.rule.enums.CheckNodeTypeEnum;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.enums.department.DepartmentTypeEnum;
import com.ruijing.store.warehouse.message.constant.WarehouseConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 * @Date 2020/11/17 18:15
 * @Description
 **/
@Service
public class BuyerOrderServiceImpl implements BuyerOrderService {
    /**
     * 发货提醒缓存
     */
    private final String ORDER_DELIVERY_REMIND_REDIS_KEY = "BuyerOrderServiceImpl_deliveryRemind_OrderId_";

    private static final String CAT_TYPE = "BuyerOrderService";

    private static Logger logger = LoggerFactory.getLogger(BuyerOrderServiceImpl.class);

    /**
     * 撤销入库，待结算也能提交入库，不生效的单位
     */
    private final static List<Integer> WAIT_STATE_INBOUND_NOT_VALID_ORG = New.list(OrgEnum.ZHONG_SHAN_DA_XUE.getValue(),
            OrgEnum.ZHONG_SHAN_DA_XUE_SHEN_ZHEN.getValue(), OrgEnum.ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN.getValue(),
            OrgEnum.GUANG_DONG_YAO_KE_DA_XUE.getValue());

    /**
     * 撤销入库，结算中或结算完成也能提交入库，不生效的单位
     */
    private final static List<Integer> STATE_INBOUND_NOT_VALID_ORG = New.list(OrgEnum.ZHONG_SHAN_DA_XUE.getValue(),
            OrgEnum.ZHONG_SHAN_DA_XUE_SHEN_ZHEN.getValue(), OrgEnum.ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN.getValue(),
            OrgEnum.GUANG_DONG_YAO_KE_DA_XUE.getValue(), OrgEnum.GUANG_XI_ZHONG_LIU.getValue());

    /**
     * 线下单不对接推送的单位
     */
    private final static List<Integer> offlineNoPushOrgSet = New.list(OrgEnum.QING_HUA_DA_XUE_GU_JI_YAN_JIU_SHENG_YUAN.getValue()
            , OrgEnum.BEI_JING_DA_XUE_SHEN_ZHEN_YAN_JIU_SHENG_YUAN.getValue());


    /**
     * 动态配置的 完成后验收时间限制秒数
     */
    @PearlValue(key="order.accept.limit.seconds", defaultValue = "7776000")
    private Long limitSeconds;

    /**
     * 默认超时天数
     */
    @PearlValue(key = "defaultTimeoutNumberDay",defaultValue = "60")
    private Integer defaultTimeoutNumberDay;

    /**
     * 自定义单据分类的orgId列表，默认广东农科院141
     */
    @PearlValue(key="SELF_DEFINED_CATEGORY_ORG_LIST", defaultValue = "[141]")
    private static List<Integer> selfDefinedCategoryOrgIdList;

    /**
     *
     */
    @PearlValue(key="NEED_PURCHASE_TYPE_ORG_LIST", defaultValue = "[141]")
    private static List<Integer> needPurchaseTypeOrgIdList;

    @Resource
    private StoreRuleClient storeRuleClient;

    @Resource
    private UserClient userClient;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private TimeoutStatisticsMapper timeoutStatisticsMapper;

    @Resource
    private OrderContractMapper orderContractMapper;

    @Resource
    private DockingExtraMapper dockingExtraMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private GoodsReturnLogDOMapper goodsReturnLogDOMapper;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private CancelOrderManageService cancelOrderManageService;

    @Resource
    private SYSUOrderService sysuOrderService;

    @Resource
    private OrderPicMapper orderPicMapper;

    @Resource
    private DepartmentRpcClient departmentRpcClient;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private CreditServiceClient creditServiceClient;

    @Resource
    private SuppClient suppClient;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private OrderAcceptService orderAcceptService;

    @Resource
    private OrderEmailHandler orderEmailHandler;

    @Resource
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Resource
    private OrderConfirmForTheRecordDOMapper confirmForTheRecordMapper;

    @Resource
    private OrderDetailRelatedService orderDetailRelatedService;

    @Resource
    private ElectronicSignServiceClient electronicSignServiceClient;

    @Resource
    private OrderAddressRPCClient orderAddressRPCClient;

    @Resource(name ="defaultIoExecutor")
    private Executor defaultIoExecutor;

    @Resource
    private OrderMasterForTPIService orderMasterForTPIService;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private OrderPushEventStatusClient orderPushEventStatusClient;

    @Resource
    private AcceptApprovalClient acceptApprovalClient;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private InvoiceClient invoiceClient;

    @Resource
    private OldDateService oldDateService;

    @Resource
    private OrderMessageHandler orderMessageHandler;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private OrderContractClient orderContractClient;

    @Resource
    private OrderUploadFileRpcClient orderUploadFileRpcClient;

    @Resource
    private OrderDetailExtraClient orderDetailExtraClient;

    @Resource
    private OrderAcceptCommentClient orderAcceptCommentClient;

    @Resource
    private AddressClient addressClient;

    @Resource
    private OrderBankSnapshotClient orderBankSnapshotClient;

    @Resource
    private OrderAuditSamplingClient orderAuditSamplingClient;

    @Resource
    private OrderContractService orderContractService;

    @Resource
    private ClaimServiceClient claimServiceClient;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    /**
     * 不展示订单状态为对接失效的组织机构
     */
    private final List<String> DECKING_FAIL_NOT_SHOW_IN_ORDER_LIST_ORGANIZATIONS = New.list(
            OrgEnum.HUA_NAN_NONG_YE_DA_XUE.getCode(),
            OrgEnum.GUANG_ZHOU_YI_KE_DA_XUE.getCode());

    /**
     * 不展示撤销入库的单位
     */
    private final List<Integer> NOT_SHOW_CANCEL_RECEIPT_ORG_LIST = New.list(OrgEnum.AN_HUI_SHENG_LI_YI_YUAN.getValue(), OrgEnum.ZHONG_SHAN_SAN_YUAN_LIN_CHUANG.getValue());


    /**
     * 需要展示上传发票状态的单位
     */
    private final List<Integer> SHOW_UPLOAD_INVOICE_ORG_LIST = New.list(OrgEnum.GUANG_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getValue(),
            OrgEnum.GUANG_ZHOU_HU_XI_JIAN_KANG_YAN_JIU_YUAN.getValue());

    /**
     * 追加上传图片，可以先删除再插入验收照片的单位
     */
    private final Set<Integer> DELETE_AND_UPDATE_ACCEPTANCE_PHOTO_ORG_SET = New.set(OrgEnum.GUANG_XI_ZHONG_LIU.getValue());

    /**
     * @param request
     * @return com.ruijing.store.order.base.core.vo.myorderlist.OrderListRespVO
     * @description: 获取www-订单管理-我的订单 列表
     * @date: 2020/12/3 9:42
     * @author: zengyanru
     */
    @Override
    @ServiceLog(description = "获取旧采购管理页面我的订单概览",serviceType = ServiceType.RPC_SERVICE_PAGE_RES)
    public PageableResponse<OrderListRespVO> getOrderListForWWW(OrderListRequest request, LoginUserInfoBO loginInfo, boolean isHms) {
        // 入参控制
        this.orderListReqControll(request);

        // 登录相关信息
        List<Integer> deptIdList = loginInfo.getDeptIdList();
        Integer orgId = loginInfo.getOrgId();
        String orgCode = loginInfo.getOrgCode();
        Integer curUserId = loginInfo.getUserId();

        // 空部门则不展示
        if (CollectionUtils.isEmpty(deptIdList)) {
            OrderListRespVO emptyResp = new OrderListRespVO(new ArrayList<>(), 0);
            return PageableResponse.<OrderListRespVO>custom().setData(emptyResp).setPageSize(request.getPageSize()).setPageNo(request.getPageNo()).setTotal(0).setSuccess();
        }

        // 构造搜索参数
        OrderSearchParamDTO param = this.constructOrderSearchParam(request, loginInfo, isHms);
        if (!this.constructTimeOutParams(param, request)) {
            // 获取查询条件时互斥，则返回空
            OrderListRespVO emptyResp = new OrderListRespVO(new ArrayList<>(), 0);
            return PageableResponse.<OrderListRespVO>custom().setData(emptyResp).setPageSize(request.getPageSize()).setPageNo(request.getPageNo()).setTotal(0).setSuccess();
        }
        SearchPageResultDTO<OrderMasterSearchDTO> response = orderSearchBoostService.commonSearch(param);

        // 从搜索中获取对应信息
        Long totalHit = response.getTotalHits();
        Integer totalPage = (int) Math.ceil((double) totalHit.intValue() / (double) request.getPageSize());
        List<OrderMasterSearchDTO> masterSearchList = response.getRecordList();
        if (CollectionUtils.isEmpty(masterSearchList)) {
            OrderListRespVO emptyResp = new OrderListRespVO(new ArrayList<>(), 0).setTestCount(0L);
            return PageableResponse.<OrderListRespVO>custom().setData(emptyResp).setPageSize(request.getPageSize()).setPageNo(request.getPageNo()).setTotal(totalHit).setSuccess();
        }
        Long testOrderCount = 0L;
        if(Boolean.TRUE.equals(request.getShowTestOrderCount())){
            testOrderCount = this.getTestCount(orgId, param);
        }

        List<Integer> orderIdList = masterSearchList.stream().map(OrderMasterSearchDTO::getId).collect(Collectors.toList());
        List<String> orderNoList = masterSearchList.stream().map(OrderMasterSearchDTO::getForderno).collect(Collectors.toList());

        // 批量查询配置（下述会用到的配置项）
        String confAcceptanceWay = OrderAcceptWayEnum.CONFIG_CODE_PROCUREMENT_ACCEPTANCE_WAY;
        String confAcceptTimeout = TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getCode();
        String confBalanceTimeout = TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode();
        String confContractUpload = ConfigCodeEnum.ORDER_CONTRACT_UPLOAD_JSON.name();
        String confReceivePic = ConfigConstant.ORG_RECEIPT_PIC_CONFIG;
        String confUseWarehouse = ConfigConstant.USE_WAREHOUSE_SYSTEM;
        String confUseWarehouseVersion = ConfigConstant.WAREHOUSE_SYSTEM_VERSION_CODE;
        String confUseStatementOnline = ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM;
        String confUseStatementOffline = ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM;
        String confShowPrintDeliveryNoteStatus = PrintConfigConstant.SHOW_PRINT_DELIVERY_NOTE_BUTTON_STATUS;
        String confShowPrintAcceptanceStatus = PrintConfigConstant.SHOW_PRINT_ACCEPTANCE_BUTTON_STATUS;
        String confShowPrintWareHouseApplicationStatus = PrintConfigConstant.SHOW_PRINT_WAREHOUSE_APPLICATION_BUTTON_STATUS;
        String offLineInvoiceConfig = ConfigCodeEnum.OFFLINE_ORDER_REQUIRE_FILL_IN_INVOICE.name();
        String isAllReturnConfig = ConfigCodeEnum.ORDER_RETURN_ONLY_WHOLE.name();

        List<String> configCodeList = New.list(confAcceptanceWay, confAcceptTimeout, confBalanceTimeout,
                confContractUpload, confReceivePic, confUseWarehouse, confUseWarehouseVersion, confUseStatementOnline,
                confUseStatementOffline, confShowPrintDeliveryNoteStatus, confShowPrintAcceptanceStatus,
                confShowPrintWareHouseApplicationStatus, offLineInvoiceConfig, isAllReturnConfig);

        List<BaseConfigDTO> baseConfigList = sysConfigClient.getValueByOrgCodeAndConfigCode(orgCode, configCodeList);
        Map<String, List<BaseConfigDTO>> codeBaseConfigMap = baseConfigList.stream().collect(groupingBy(BaseConfigDTO::getConfigCode));
        Map<String, List<String>> baseConfigCodeValueMap = baseConfigList.stream().collect(groupingBy(BaseConfigDTO::getConfigCode, Collectors.mapping(BaseConfigDTO::getConfigValue, toList())));

        // 设置供应商相关信息，联系信息
        List<Integer> suppIdList = masterSearchList.stream().filter(o -> Objects.equals(o.getSpecies(), SpeciesEnum.ONLINE.getValue())).map(OrderMasterSearchDTO::getFsuppid).collect(toList());

        Map<Integer, SuppShopInfoBO> suppContactInfoMap = null;
        try {
            suppContactInfoMap = suppClient.getSupplierContactInfoMap(suppIdList, orgId);
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "getOrderListForWWW", e.getMessage(), e);
            suppContactInfoMap = new HashMap<>();
        }
        // 订单的额外信息，异步获取
        CompletableFuture<List<OrderExtraDTO>> orderExtraDataFuture = AsyncExecutor.callAsync(
                () -> orderExtraClient.selectByOrderIdAndExtraKey(orderIdList, this.getQueryOrderExtraKeyList(orgId)));

        // 验收后的提交结算方式
        List<BaseConfigDTO> acceptBaseConfigList = codeBaseConfigMap.get(confAcceptanceWay);
        List<String> configValueList = acceptBaseConfigList.stream().map(BaseConfigDTO::getConfigValue).collect(toList());
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(acceptBaseConfigList), ExecptionMessageEnum.UNIT_ACCEPTANCE_CONFIG_NOT_FOUND);
        OrderAcceptWayEnum confAccept = OrderAcceptWayEnum.getByValue(Integer.valueOf(configValueList.get(0)));

        // 验收、结算超时订单列表
        OrderOvertimeBO orderOvertimeBO = this.findOvertimeOrderIdList(masterSearchList, orgId, codeBaseConfigMap);

        // 对接状态docking extra
        Map<String, DockingExtra> orderNoDockStatusMap = this.constructOrderNoDockStatusMap(orderNoList);

        // 对接状态docking extra
        Map<String, List<OrderPushEventStatusVO>> orderNoPushEventStatusMap = this.constructOrderNoPushEventStatusMap(orderNoList,orgCode);
        // 验收审批等级
        Map<Integer, AcceptApproveInfoDTO> orderIdCurrentAcceptApproveLevelMap = this.constructOrderIdCurrentAcceptApproveLevelMap(orderIdList);


        // 通过orderid和orgcode查询statement（结算单），构造map避免循环查表,取查表第一个的结算单(store逻辑）
        Map<Long, StatementResultDTO> statementIdResultMapping = statementPlatformClient.constructStatementIdMap(masterSearchList, orgCode);

        // 订单评价状态mapping
        Map<Integer, Short> orderIdCommentStatusMap = creditServiceClient.getOrderCommentStatusMap(orderIdList);

        // 合同详情的展示
        Map<Integer, Integer> orderIdContractStatusMap = this.orderUploadContractInfo(codeBaseConfigMap, masterSearchList, orgId);

        Boolean useNewWarehouseSystem = this.isUseNewWarehouseSystem(codeBaseConfigMap);

        // 经费卡释放失败的话需要单独查询数据库
        Map<Integer, OrderMasterDO> orderIdMasterMap = null;
        List<Integer> needQryDbIdList = masterSearchList.stream()
                .filter(s -> OrderFundStatusEnum.ThrawFailed.getValue().equals(s.getFundStatus())
                        || OrderFundStatusEnum.FreezedFail.getValue().equals(s.getFundStatus())
                        || OrderFundStatusEnum.DEDUCT_FAILED.getValue().equals(s.getFundStatus())
                        || OrderFundStatusEnum.ChangedCardFail.getValue().equals(s.getFundStatus())
                )
                .map(OrderMasterSearchDTO::getId).collect(toList());
        if (CollectionUtils.isNotEmpty(needQryDbIdList)) {
            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(needQryDbIdList);
            orderIdMasterMap = DictionaryUtils.toMap(orderMasterDOList, OrderMasterDO::getId, Function.identity());
        }

        List<OrderDeliveryInfoDTO> orderDeliveryInfoDTOList = suppClient.queryOrderDeliveryInfo(masterSearchList.stream().filter(item->OrderStatusEnum.WaitingForReceive.getValue().equals(item.getStatus())).map(OrderMasterSearchDTO::getId).collect(toList()));
        Map<Integer, OrderDeliveryInfoDTO> orderIdDeliveryInfoMap = DictionaryUtils.toMap(orderDeliveryInfoDTOList, OrderDeliveryInfoDTO::getOrderId, Function.identity());

        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.selectOrderListDetailByOrderId(orderIdList);
        Map<Integer, List<OrderDetailDO>> orderIdDetailMap = DictionaryUtils.groupBy(orderDetailDOList, OrderDetailDO::getFmasterid);

        // 是否使用锐竞的结算系统
        Boolean onlineStatementCheck = true;
        Boolean offlineStatementCheck = true;
        if (CollectionUtils.isNotEmpty(codeBaseConfigMap.get(confUseStatementOnline))) {
            onlineStatementCheck = ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE.equals(codeBaseConfigMap.get(confUseStatementOnline).get(0).getConfigValue());
        }
        if (CollectionUtils.isNotEmpty(codeBaseConfigMap.get(confUseStatementOffline))) {
            offlineStatementCheck = ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE.equals(codeBaseConfigMap.get(confUseStatementOffline).get(0).getConfigValue());
        }

        // 备案记录
        Map<Integer, OrderConfirmForTheRecordDO> confirmForTheRecordMap = this.getConfirmForTheRecordMap(orderIdList
                , curUserId, deptIdList, New.list(ConfigConstant.ORDER_CONFIRM_FOR_THE_RECORD), orgId);

        // hms额外逻辑
        Map<Integer, OrderOfflineInfoVO> orderIdOfflineInfoMap = new HashMap<>();
        if (isHms) {
            // 特殊单位（中大）显示的订单时间
            this.setSpecialOrgOrderDate(orgId, masterSearchList);
        }
        if (request.getNeedOfflineInfo()) {
            // 线下单相关信息
            List<OrderOfflineInfoVO> offlineInfoList = orderDetailRelatedService.getOfflineInfoList(masterSearchList);
            orderIdOfflineInfoMap = offlineInfoList.stream().collect(toMap(OrderOfflineInfoVO::getOrderId, Function.identity(), (oldVal, newVal) -> oldVal));
        }

        // 订单验收图片字段，只有广西肿瘤用
        Map<String, List<OrderPic>> orderNoPicMap = this.constructOrderPicMap(orgCode, orderNoList);
        // 获取课题组id-电子签名配置映射
        Map<Integer, ElectronicSignConfigVO> buyerDepartmentIdESignConfigMap = this.getBuyerDepartmentIdESignConfigMap(masterSearchList, loginInfo);

        // 代配送
        Map<Integer, OrderAddressDTO> orderIdAddressMap = this.getOrderDeliveryProxySetting(orderIdList);
        // 查商家申请取消代配送的订单，对应最后一条商家申请取消记录
        List<Integer> applyCxDelPxyOrderIdList = orderIdAddressMap.values().stream()
                .filter(orderAddressDTO -> DeliveryTypeEnum.PROXY.getCode().equals(orderAddressDTO.getDeliveryType())
                        && DeliveryStatusEnum.SUPP_APPLY_CANCEL.getValue().equals(orderAddressDTO.getDeliveryStatus()))
                .map(OrderAddressDTO::getId).collect(Collectors.toList());
        Map<Integer,OrderApprovalLog> orderIdCancelCxDelPxyLogMap = New.emptyMap();
        if(CollectionUtils.isNotEmpty(applyCxDelPxyOrderIdList)){
            List<OrderApprovalLog> orderApprovalLogList = orderApprovalLogMapper.findByOrderIdInAndApproveStatusIn(applyCxDelPxyOrderIdList, New.list(OrderApprovalEnum.SUPP_APPLY_CANCEL_DELIVERY_PROXY.getValue()));
            orderIdCancelCxDelPxyLogMap = DictionaryUtils.toMap(orderApprovalLogList, OrderApprovalLog::getOrderId, Function.identity(), (oldVal,newVal)-> newVal.getId() > oldVal.getId() ? newVal : oldVal);
        }

        Map<Integer, List<String>> suppIdDeliveryProxyLabel = suppClient.getSuppIdDeliveryProxyLabel(orgId, suppIdList);

        // 异步任务完成，（自定义分类标签、todo：后续需要将其他部分纳入异步任务）
        Map<Integer, List<OrderExtraDTO>> orderIdExtraListMap = New.emptyMap();
        try {
            orderIdExtraListMap = DictionaryUtils.groupBy(Optional.ofNullable(orderExtraDataFuture.get()).orElse(New.emptyList()), OrderExtraDTO::getOrderId);
        } catch (Exception e) {
            logger.error("获取订单拓展字段失败");
        }

        // 权限获取
        Map<String, List<DepartmentDTO>> accessCodeDeptMap = userClient.findUserHasAccessDepartmentBatch(curUserId, orgId, New.list(ConfigConstant.ORDER_ACCEPTANCE,
                WarehouseConstant.BUYER_CENTER_SUBMISSION_STORAGE,
                ConfigConstant.ORDER_SAMPLING_INSPECTION_CODE,
                BuyerCenterAccessConstant.SERVICE_ORDER_ACCEPTANCE,
                BuyerCenterAccessConstant.NON_SERVICE_ORDER_ACCEPTANCE,
                ConfigConstant.REGULATORY_ORDER_ACCEPTANCE,
                ConfigConstant.NON_REGULATORY_ORDER_ACCEPTANCE));
        Map<String, List<Integer>> accessCodeDeptIdMap = New.mapWithCapacity(accessCodeDeptMap.size());
        for(Map.Entry<String, List<DepartmentDTO>> e : accessCodeDeptMap.entrySet()){
            accessCodeDeptIdMap.put(e.getKey(), e.getValue().stream().map(DepartmentDTO::getId).collect(toList()));
        }
        accessCodeDeptMap = null;

        // 经费卡信息
        Map<Integer, List<OrderFundcardVO>> orderIdFundCardVoMap = this.getOrderIdFundCardVOMap(orgCode, masterSearchList);
        // 撤销入库按钮
        boolean showCancelReceiptCtrl = false;
        if (!NOT_SHOW_CANCEL_RECEIPT_ORG_LIST.contains(orgId)) {
            showCancelReceiptCtrl = userClient.checkUserInDepartmentAccess(curUserId, deptIdList, New.list(ConfigConstant.BUYER_CENTER_CANCEL_RECEIPT), orgId);
        }
        // 有发票的订单id
        List<Integer> uploadedOrderIdList = null;
        List<String> offLineInvoiceConfigList = baseConfigCodeValueMap.get(offLineInvoiceConfig);
        String offLineInvoiceConfigValue = CollectionUtils.isNotEmpty(offLineInvoiceConfigList) ? offLineInvoiceConfigList.get(0) : ConfigCodeEnum.OFFLINE_ORDER_REQUIRE_FILL_IN_INVOICE.getValue();
        // 根据OMS配置检查线下单是否必填发票, 1不填，2.非必填，3必填
        String checkInvoiceConfig = "3";
        if (SHOW_UPLOAD_INVOICE_ORG_LIST.contains(orgId) || checkInvoiceConfig.equals(offLineInvoiceConfigValue)) {
            // 广州医科大和oms为3必填发票， 无发票无法收货
            List<InvoiceDTO> invoiceDTOList = invoiceClient.findInvoiceList(orderIdList, orgId);
            uploadedOrderIdList = invoiceDTOList.stream().map(InvoiceDTO::getSourceId).map(Long::intValue).distinct().collect(toList());
        }
        // 需要展示的旧单时间
        OldDateConfigBO oldDateConfigBO = oldDateService.getOldDateConfig(orgId);
        OrgDockingConfigDTO dockingConfig = dockingConfigCommonService.getConfig(orgCode);

        Map<String, OrderAuditSamplingDTO> orderNoAuditSamplingMap = New.emptyMap();
        if(isHms){
            List<OrderAuditSamplingDTO> orderAuditSamplingDTOList = orderAuditSamplingClient.listByOrderNos(orderNoList);
            orderNoAuditSamplingMap = CollectionUtils.isEmpty(orderAuditSamplingDTOList) ? New.emptyMap() : DictionaryUtils.toMap(orderAuditSamplingDTOList, OrderAuditSamplingDTO::getOrderNo, Function.identity());
        }

        // 构造入参和出参
        OrderListRequireInfoBO orderListRequireInfo = new OrderListRequireInfoBO();
        orderListRequireInfo.setRequest(request).setOrgId(orgId).setOrgCode(orgCode).setCurUserId(curUserId).setCurUserGuid(loginInfo.getUserGuid())
                .setStatementIdResultMapping(statementIdResultMapping)
                .setOrderIdCommentStatusMap(orderIdCommentStatusMap).setOrderIdContractStatusMap(orderIdContractStatusMap)
                .setOrderOvertimeBO(orderOvertimeBO).setOrderNoDockStatusMap(orderNoDockStatusMap)
                .setOrderNoPushEventStatusMap(orderNoPushEventStatusMap).setOrderIdCurrentAcceptApproveInfoMap(orderIdCurrentAcceptApproveLevelMap)
                .setUseNewWarehouseSystem(useNewWarehouseSystem).setConfAccept(confAccept).setSuppContactInfoMap(suppContactInfoMap)
                .setOrderIdMasterMap(orderIdMasterMap).setOnlineStatementCheck(onlineStatementCheck)
                .setOfflineStatementCheck(offlineStatementCheck).setOrderIdConfirmRecordMap(confirmForTheRecordMap)
                .setHmsCheck(isHms)
                .setOrderIdOfflineInfoMap(orderIdOfflineInfoMap)
                .setOrderNoPicMap(orderNoPicMap).setOrderIdAddressMap(orderIdAddressMap)
                .setSuppIdDeliveryProxyLabel(suppIdDeliveryProxyLabel)
                .setOrderIdExtraMap(orderIdExtraListMap)
                .setBuyerDepartmentIdSignConfigMap(buyerDepartmentIdESignConfigMap)
                .setShowCancelReceiptCtrl(showCancelReceiptCtrl)
                .setOrderIdCancelCxDelPxyLogMap(orderIdCancelCxDelPxyLogMap)
                .setUploadedInvoiceOrderList(uploadedOrderIdList)
                .setOrderIdFundCardVoMap(orderIdFundCardVoMap)
                .setUserIdNameMap(New.map())
                .setOldDateConfigBO(oldDateConfigBO)
                .setDockingConfig(dockingConfig)
                .setEnableDockingConfigOrderIdList(dockingConfigCommonService.getEnableNewDockingOrderList(dockingConfig, masterSearchList))
                .setOrderIdWarehouseRejectReasonMap(this.getWarehouseRejectReasonMap(orgId, masterSearchList))
                .setBaseConfigCodeValueMap(baseConfigCodeValueMap)
                .setOrderIdUploadFileMap(this.getOrderIdUploadFileMap(orgId,orderIdList))
                .setOrderIdAccountTypeMap(this.getOrderAccountTypeMap(masterSearchList,orgId))
                .setOrderIdDetailExtraMap(this.getOrderIdDetailExtraMap(orderIdList))
                .setOrderDetailAcceptanceFilesMap(this.getOrderDetailAcceptanceFilesMap(orderIdList))
                .setOrderDetailAcceptancePicsMap(this.getOrderDetailAcceptancePicsMap(orderIdList))
                .setOrderNoContractInfoMap(this.getOrderNewContractInfoMap(orderNoList))
                .setAccessCodeDeptIdMap(accessCodeDeptIdMap).setOrderNoAuditSamplingMap(orderNoAuditSamplingMap)
                .setOrderIdOrderDetailMap(orderIdDetailMap).setOrderIdDeliveryInfoMap(orderIdDeliveryInfoMap)
                ;

        List<OrderInfoVO> modelRespList = new ArrayList<>(masterSearchList.size());
        // 搜索完毕后，需要设定当前登录用户供其他检查使用
        request.setFbuyerId(loginInfo.getUserId());
        request.setFbuyername(loginInfo.getUserName());
        for (OrderMasterSearchDTO masterSearch : masterSearchList) {
            OrderInfoVO orderInfo = this.constructOrderRespInfo(orderListRequireInfo, masterSearch);
            modelRespList.add(orderInfo);
        }

        OrderListRespVO orderListRespVO = new OrderListRespVO(modelRespList, totalPage).setTestCount(testOrderCount);
        orderListRespVO.setPhotoAcceptance(this.getPhotoAcceptance(codeBaseConfigMap));
        orderListRespVO.setAcceptancePrint(userClient.checkUserInDepartmentAccess(curUserId, deptIdList, New.list(ConfigConstant.ACCEPTANCE_PRINT), orgId));
        // 设置验收审批权限
        setApprovalAccess(orderListRespVO, request, loginInfo);
        // 设置打印按钮显隐
        this.setShowPrintButton(orderListRespVO, baseConfigCodeValueMap);
        this.customOrgOldFlag(orgCode, orderListRespVO);
        this.setCustomTag(orgId, orderListRespVO);
        return PageableResponse.<OrderListRespVO>custom().setData(orderListRespVO).setTotal(totalHit).setPageNo(request.getPageNo()).setPageSize(request.getPageSize()).setSuccess();
    }

    /**
     * 查询订单详情扩展信息
     *
     * @param orderIdList 订单id列表
     * @return 订单详情扩展信息 key - orderId value - List<OrderDetailExtraDTO>
     */
    public Map<Integer, List<com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO>> getOrderIdDetailExtraMap(List<Integer> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return New.emptyMap();
        }
        Map<Integer, List<com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO>> orderId2DetailExtraMap = New.map();
        List<com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO> orderDetailExtraDTOS = orderDetailExtraClient.listOrderDetailExtra(orderIdList, null);

        // 遍历orderDetailExtraDTOS，将每个OrderDetailExtraDTO按照orderId分组
        for (com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO detailExtraDTO : orderDetailExtraDTOS) {
            Integer orderId = detailExtraDTO.getOrderId();
            // 获取当前orderId对应的List，如果不存在则创建新的List
            List<com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO> detailExtraList = orderId2DetailExtraMap.computeIfAbsent(orderId, k -> New.list());
            // 将当前的detailExtraDTO加入到对应的List中
            detailExtraList.add(detailExtraDTO);
        }

        // 填充没有Map的orderId
        for (Integer orderId : orderIdList) {
            orderId2DetailExtraMap.putIfAbsent(orderId, New.emptyList());
        }
        return orderId2DetailExtraMap;
    }

    /**
     * 设置订单验收审批权限
     * @param orderListRespVO   订单列表出参
     * @param request           查询入参
     * @param loginInfo         登录信息
     */
    private void setApprovalAccess(OrderListRespVO orderListRespVO, OrderListRequest request, LoginUserInfoBO loginInfo) {
        List<Integer> orderIdList = orderListRespVO.getOrderList().stream().map(orderInfoVO -> orderInfoVO.getOrder().getId()).collect(toList());
        List<Integer> hasApprovalAccessOrderIdList = acceptApprovalClient.filterOrderUserHasAuthApprove(loginInfo.getOrgId(), loginInfo.getUserId(), orderIdList);

        for (OrderInfoVO orderInfoVO : orderListRespVO.getOrderList()) {
            orderInfoVO.setApprovalPermit(hasApprovalAccessOrderIdList.contains(orderInfoVO.getOrder().getId()));
        }
    }

    /**
     * 设置特殊单位的订单时间
     * @param orgId
     * @param masterSearchList
     */
    private void setSpecialOrgOrderDate(Integer orgId, List<OrderMasterSearchDTO> masterSearchList) {
        if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(orgId)) {
            Set<Integer> applyIdSet = masterSearchList.stream().map(OrderMasterSearchDTO::getFtbuyappid).collect(toSet());
            List<PurchaseApprovalLogDTO> approveLogList = purchaseApprovalLogClient.getApprovalLogByResult(applyIdSet, "审批通过", ApprovalDockingTypeEnum.PURCHASE_APPLY);
            Map<Integer, List<PurchaseApprovalLogDTO>> applyIdApproveLogMap = approveLogList.stream().collect(groupingBy(PurchaseApprovalLogDTO::getApplicationId));
            for (int i = 0; i < masterSearchList.size(); i++) {
                Integer applyId = masterSearchList.get(i) == null ? null : masterSearchList.get(i).getFtbuyappid();
                List<PurchaseApprovalLogDTO> curApproveLogList = applyIdApproveLogMap.get(applyId);
                Date firstApproveDate = null;
                if (CollectionUtils.isNotEmpty(curApproveLogList) && curApproveLogList.get(0) != null) {
                    firstApproveDate = curApproveLogList.get(0).getApproveTime();
                }
                String firstApproveDateString = firstApproveDate == null ? masterSearchList.get(i).getForderdate() : DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, firstApproveDate);
                masterSearchList.get(i).setForderdate(firstApproveDateString);
            }
        }
    }

    /**
     * 构造订单列表搜索请求体
     * @param request
     * @param loginInfo
     * @param isHms
     * @return
     */
    public OrderSearchParamDTO constructOrderSearchParam(OrderListRequest request, LoginUserInfoBO loginInfo, boolean isHms) {
        Integer rootDepartmentId = loginInfo.getRootDepartmentId();
        Integer orgId = loginInfo.getOrgId();
        String orgCode = loginInfo.getOrgCode();
        Integer curUserId = loginInfo.getUserId();
        List<DepartmentDTO> deptList = loginInfo.getDeptList();

        // 构造搜索的请求体，用户普通展示列表和有查询条件的展示列表
        OrderSearchParamDTO param = new OrderSearchParamDTO();
        param.setPageSize(request.getPageSize());

        // 我的已审批查询条件
        param.setApproveStatusList(request.getApproveStatusList());
        param.setOperatorId(request.getOperatorId());

        // 单位&采购人处理
        request.setOrgId(orgId);
        request.setOrgCode(orgCode);

        // hms额外逻辑(搜索条件构建
        if (isHms) {
            this.handleParamForHms(param, request, loginInfo);
        }

        // 搜索条件构建和搜索
        if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(orgId)) {
            sysuOrderService.controllViewOrderAccessSYSU(param, request, rootDepartmentId, loginInfo);
        } else {
            List<Integer> deptIdListForSearch = this.getDeptIdListForSearch(deptList, request, rootDepartmentId);
            request.setDeptIdList(deptIdListForSearch);
            this.constructSearchPageParam(param, request);
        }
        this.setExcludeStatusStrategy(orgCode, param);
        // 全平台增加入库状态查询
        Integer inventoryStatus = request.getInventoryStatus();
        if(inventoryStatus != null) {
            param.setInventoryStatusList(New.list(inventoryStatus));
        }
        // 全平台增加根据结算状态查询
        List<Integer> statementStatusList = request.getStatementStatusList();
        if(CollectionUtils.isNotEmpty(statementStatusList)){
            param.setStatementStatusList(statementStatusList);
        }
        // 额外信息查询
        List<OrderExtraInfoParamDTO> orderExtraInfoList = request.getOrderExtraInfoList();
        if (Objects.isNull(orderExtraInfoList)) {
            orderExtraInfoList = New.list();
        }

        // 订单标签过滤
        Integer orderTag = request.getOrderTag();
        if (Objects.nonNull(orderTag)) {
            OrderExtraInfoParamDTO orderTagParam = new OrderExtraInfoParamDTO();
            orderTagParam.setOrderExtraEnum(OrderExtraEnum.ORDER_TAG);
            orderTagParam.setOrderExtraValue(request.getOrderTag().toString());
            orderExtraInfoList.add(orderTagParam);
        }

        param.setOrderExtraInfoList(orderExtraInfoList);
        // 经费类型过滤条件
        param.setFundType(request.getFundType());
        param.setAcceptApproveLevel(request.getAcceptApproveLevel());
        // 是否需要定制排序
        this.hasCustomSortRule(param);
        return param;
    }


    /**
     * 是否需要定制排序
     *
     * @return
     */
    private void hasCustomSortRule(OrderSearchParamDTO orderSearchParamDTO){
        List<Integer> statusList = orderSearchParamDTO.getStatusList();
        // 福建肿瘤要求根据不同状态 按不同字段排序
        if (OrgEnum.FU_JIAN_SHENG_ZHONG_LIU_YI_YUAN.getCode().equals(orderSearchParamDTO.getOrgCode())) {
            if (statusList.contains(OrderStatusEnum.OrderReceiveApproval.getValue())) {
                this.setCustomSortField(orderSearchParamDTO, "flastreceivedate", SortOrderEnum.DESC);
            } else if (statusList.contains(OrderStatusEnum.Statementing_1.getValue())) {
                this.setCustomSortField(orderSearchParamDTO, "in_state_time", SortOrderEnum.DESC);
            }
        }

        if (OrgEnum.XIA_MEN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode().equals(orderSearchParamDTO.getOrgCode())) {
            if (statusList.contains(OrderStatusEnum.OrderReceiveApproval.getValue())) {
                this.setCustomSortField(orderSearchParamDTO, "flastreceivedate", SortOrderEnum.DESC);
            }
        }
        return;
    }

    private void setCustomSortField(OrderSearchParamDTO param, String field, SortOrderEnum order) {
        FieldSortDTO fieldSortDTO = new FieldSortDTO(field, order);
        param.setFieldSortList(New.list(fieldSortDTO));
        param.setOrderDateSort(null);
    }
    /**
     * 处理hms特殊逻辑
     * @param param 搜索参数
     * @param request 传入参数
     * @param loginInfo 登录信息
     */
    private void handleParamForHms(OrderSearchParamDTO param, OrderListRequest request, LoginUserInfoBO loginInfo) {
        // hms额外逻辑(搜索条件构建
        // 审批流控制，用户所在部门所拥有的角色是否拥有某些单据的查看权限,特指-1是全部都可看（旧单和竞价单）
        List<Integer> flowIdsForUser = purchaseApprovalLogClient.findFlowIdsForUser(loginInfo.getUserId(), loginInfo.getOrgId(), FlowRoleAccessEnum.VIEW_ORDER);
        Integer allPrivilegeFlowId = -1;
        flowIdsForUser.add(allPrivilegeFlowId);
        param.setFlowIdList(flowIdsForUser);

        // 管制品搜索
        this.setConfirmDangerousSearch(request, param);
        // hms不展示单位对接单
        param.getExcludeStatusList().add(OrderStatusEnum.WaitingForDockingConfirm.getValue());
    }

    /**
     * 根据订单id查询订单是否代配送
     * @param orderIdList
     * @return
     */
    private Map<Integer, OrderAddressDTO> getOrderDeliveryProxySetting(List<Integer> orderIdList) {
        List<OrderAddressDTO> orderAddressList = orderAddressRPCClient.findByOrderId(orderIdList);
        if (CollectionUtils.isEmpty(orderAddressList)) {
            return New.emptyMap();
        }
        return DictionaryUtils.toMap(orderAddressList, OrderAddressDTO::getId, Function.identity());
    }

    /**
     * 设置危化品备案的搜索值
     * @param request
     * @param param
     */
    private void setConfirmDangerousSearch(OrderListRequest request, OrderSearchParamDTO param) {
        if (Objects.equals(request.getDangerousType(), DangerousTypeEnum.UN_DANGEROUS.getValue())) {
            if (Objects.equals(request.getConfirmForTheRecord(), OrderConfirmShowPrivEnum.CONFIRM_FOR_THE_RECORD_WAIT.getValue())) {
                param.setConfirmed(OrderConfirmEnum.NO_CONFIRM.getValue());
            } else {
                param.setConfirmed(OrderConfirmEnum.UNKNOWN.getValue());
            }
        }
    }

    /**
     * 获取订单采购商品的备案对应关系，订单id-备案记录
     * @param orderIdList
     * @param curUserId
     * @param deptIdList
     * @param accessCodeList
     * @param orgId
     * @return
     */
    private Map<Integer, OrderConfirmForTheRecordDO> getConfirmForTheRecordMap(List<Integer> orderIdList
            , Integer curUserId
            , List<Integer> deptIdList
            , List<String> accessCodeList
            , Integer orgId) {
        Map<Integer, OrderConfirmForTheRecordDO> orderIdConfirmRecordMap = new HashMap<>();
        if (userClient.checkUserInDepartmentAccess(curUserId, deptIdList, accessCodeList, orgId)) {
            List<OrderConfirmForTheRecordDO> confirmRecordList = confirmForTheRecordMapper.findByOrderIdIn(orderIdList);
            if (CollectionUtils.isNotEmpty(confirmRecordList)) {
                orderIdConfirmRecordMap = confirmRecordList.stream().collect(toMap(OrderConfirmForTheRecordDO::getOrderId, Function.identity()));
            }
        }
        return orderIdConfirmRecordMap;
    }

    private void setExcludeStatusStrategy(String orgCode, OrderSearchParamDTO param) {
        // 订单列表要过滤状态为0的订单，这种状态的单只有单号对接的单位用(广工)，不需要显示在采购人中心
        if (!OrgEnum.GUANG_DONG_GONG_YE_DA_XUE.getCode().equals(orgCode) &&
            !OrgEnum.ZHONG_KAI_NONG_YE_GONG_CHENG_XUE_YUAN.getCode().equals(orgCode)) {
            param.addExcludeStatusList(New.list(OrderStatusEnum.WaitingForDockingConfirm.getValue()));
        }
        // 不需要展示订单状态为对接失效(即推送订单失败)的组织机构需要过滤掉对接失效状态的单
        if(DECKING_FAIL_NOT_SHOW_IN_ORDER_LIST_ORGANIZATIONS.contains(orgCode)){
            param.addExcludeStatusList(New.list(OrderStatusEnum.DeckingFail.getValue()));
        }
    }

    private Map<String, List<OrderPic>> constructOrderPicMap(String orgCode, List<String> orderNoList) {
        List<OrderPic> orderPics = orderPicMapper.batchSelectByOrderNo(orderNoList);
        return DictionaryUtils.groupBy(orderPics, OrderPic::getOrderNo);
    }

    private void setExcludeStatusStrategy(Map<Integer, Integer> map) {
        // 订单列表要过滤状态为-1的订单，这种状态的单只有单号对接的单位用(华师)，不需要显示在采购人中心
        map.remove(OrderStatusEnum.WaitingForDockingConfirm.getValue());
        // 去掉已被拆分的订单
        map.remove(OrderStatusEnum.ORDER_SPLIT_UP.getValue());
    }

    /**
     * @description: 组装返回部门id，名称列表
     * @date: 2021/2/25 10:39
     * @author: zengyanru
     * @param deptList 部门列表
     * @param rootDeptId 根部门id
     * @return java.util.List<com.ruijing.store.order.gateway.buyercenter.vo.orderlist.DeptBriefVO>
     */
    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "组装返回部门id，名称列表")
    public List<DeptBriefVO> constructDeptsBriefInfo(List<DepartmentDTO> deptList, Integer rootDeptId) {
        if (CollectionUtils.isEmpty(deptList)) {
            return New.list();
        }
        List<DeptBriefVO> deptBriefList = New.list();
        for (DepartmentDTO dept : deptList) {
            // 管理部门 departmenttype是1，根部门不展示
            if (dept.getDepartmentType().equals(1) || dept.getId().equals(rootDeptId)) {
                continue;
            } else {
                DeptBriefVO deptBriefVO = new DeptBriefVO();
                deptBriefVO.setId(dept.getId());
                deptBriefVO.setName(dept.getName());
                deptBriefList.add(deptBriefVO);
            }
        }
        return deptBriefList;
    }

    /**
     * @param orderListRequireInfo
     * @param masterSearch
     * @return com.ruijing.store.order.base.core.vo.myorderlist.OrderInfoVO
     * @description: 根据构建的对应关系构建订单主表与详情表返回体信息
     * @date: 2020/12/3 9:43
     * @author: zengyanru
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "根据构建的对应关系构建订单主表与详情表返回体信息")
    private OrderInfoVO constructOrderRespInfo(OrderListRequireInfoBO orderListRequireInfo, OrderMasterSearchDTO masterSearch) {
        // 需要的入参变量，从各处得来的需要展示的与订单关联的信息
        OrderListRequest request = orderListRequireInfo.getRequest();
        String orgCode = orderListRequireInfo.getOrgCode();
        Integer curUserId = orderListRequireInfo.getCurUserId();
        OrderAcceptWayEnum confAccept = orderListRequireInfo.getConfAccept();
        OrderOvertimeBO orderOvertimeBO = orderListRequireInfo.getOrderOvertimeBO();
        Map<Long, StatementResultDTO> statementIdResultMapping = orderListRequireInfo.getStatementIdResultMapping();
        Map<Integer, Short> orderIdCommentStatusMap = orderListRequireInfo.getOrderIdCommentStatusMap();
        Map<Integer, Integer> orderIdContractStatusMap = orderListRequireInfo.getOrderIdContractStatusMap();
        Boolean useNewWarehouseSystem = orderListRequireInfo.getUseNewWarehouseSystem();
        Map<String, DockingExtra> orderNoDockStatusMap = orderListRequireInfo.getOrderNoDockStatusMap();
        Map<Integer, SuppShopInfoBO> suppContactInfo = orderListRequireInfo.getSuppContactInfoMap();
        Map<Integer, OrderMasterDO> orderIdMasterMap = orderListRequireInfo.getOrderIdMasterMap();
        Map<Integer, OrderConfirmForTheRecordDO> orderIdConfirmRecordMap = orderListRequireInfo.getOrderIdConfirmRecordMap();
        Map<Integer, OrderOfflineInfoVO> orderIdOfflineInfoMap = orderListRequireInfo.getOrderIdOfflineInfoMap();
        Map<Integer, OrderAddressDTO> orderIdAddressMap = orderListRequireInfo.getOrderIdAddressMap();
        Map<Integer, List<String>> suppIdDeliveryProxyLabel = orderListRequireInfo.getSuppIdDeliveryProxyLabel();
        Map<Integer, List<OrderExtraDTO>> orderIdExtraMap = orderListRequireInfo.getOrderIdExtraMap();
        Map<String, List<OrderPushEventStatusVO>> orderNoPushEventStatusMap = orderListRequireInfo.getOrderNoPushEventStatusMap();
        Map<Integer, AcceptApproveInfoDTO> orderIdCurrentAcceptApproveLevelMap = orderListRequireInfo.getOrderIdCurrentAcceptApproveInfoMap();
        Map<Integer, ElectronicSignConfigVO> buyerDepartmentIdESignConfigMap = orderListRequireInfo.getBuyerDepartmentIdSignConfigMap();
        Map<Integer, List<OrderFundcardVO>> orderIdFundCardVoMap = orderListRequireInfo.getOrderIdFundCardVoMap();
        Map<Integer, String> orderIdWarehouseRejectReasonMap = orderListRequireInfo.getOrderIdWarehouseRejectReasonMap();
        Map<String, List<String>> baseConfigCodeValueMap = orderListRequireInfo.getBaseConfigCodeValueMap();
        Map<Integer, List<OrderDetailExtraDTO>> orderIdDetailExtraMap = orderListRequireInfo.getOrderIdDetailExtraMap();
        Map<Integer, Map<Integer, List<OrderUploadFileDTO>>> orderIdUploadFileMap = orderListRequireInfo.getOrderIdUploadFileMap();
        Map<Integer, Integer> orderIdAccountTypeMap = orderListRequireInfo.getOrderIdAccountTypeMap();
        Map<Integer, List<AcceptPictureDTO>> orderDetailAcceptancePicsMap = orderListRequireInfo.getOrderDetailAcceptancePicsMap();
        Map<Integer, List<AcceptAttachmentDTO>> orderDetailAcceptanceFilesMap = orderListRequireInfo.getOrderDetailAcceptanceFilesMap();
        Map<String, List<OrderContractInfoVO>> orderNoContractInfoMap = orderListRequireInfo.getOrderNoContractInfoMap();
        Map<Integer, OrderDeliveryInfoDTO> orderIdDeliveryInfoMap = orderListRequireInfo.getOrderIdDeliveryInfoMap();
        List<Integer> enableDockingConfigOrderIdList = orderListRequireInfo.getEnableDockingConfigOrderIdList();
        OrgDockingConfigDTO dockingConfig = orderListRequireInfo.getDockingConfig();

        Map<String, List<Integer>> accessCodeDeptIdMap = orderListRequireInfo.getAccessCodeDeptIdMap();
        List<Integer> permitWarehouseDeptIdList = accessCodeDeptIdMap.get(WarehouseConstant.BUYER_CENTER_SUBMISSION_STORAGE);

        boolean showCancelReceiptCtrl = orderListRequireInfo.isShowCancelReceiptCtrl();
        Map<Integer, OrderApprovalLog> orderIdCancelCxDelPxyLogMap = orderListRequireInfo.getOrderIdCancelCxDelPxyLogMap();
        List<Integer> uploadedInvoiceOrderList = orderListRequireInfo.getUploadedInvoiceOrderList();
        OldDateConfigBO oldDateConfigBO = orderListRequireInfo.getOldDateConfigBO();

        boolean isHms = orderListRequireInfo.isHms();
        Boolean statementCheck = OrderSpeciesEnum.NORMAL.getValue().equals(masterSearch.getSpecies()) ? orderListRequireInfo.getOnlineStatementCheck() : orderListRequireInfo.getOfflineStatementCheck();
        Map<String, List<OrderPic>> orderNoPicMap = orderListRequireInfo.getOrderNoPicMap();
        OrderMasterVO order = this.masterSearchToVo(masterSearch);
        OrderInfoVO orderInfo = new OrderInfoVO();
        //订单详情，优惠价等
        List<OrderDetailVO> orderDetailList = this.getOrderDetailsByOrder(masterSearch.getOrderDetail(), masterSearch.getStatus(), statementCheck, isHms, request.getOrgId(), orderIdDetailExtraMap.get(masterSearch.getId()), masterSearch.getStatementId(), orderListRequireInfo.getOrderIdOrderDetailMap().get(masterSearch.getId()));
        orderInfo.setOrderAndDetail(order, orderDetailList);

        // 供应商联系信息（目前只用qq）
        if (suppContactInfo.get(order.getSupplierId()) != null) {
            order.setSuppQQ(suppContactInfo.get(order.getSupplierId()).getQq());
            order.setSuppTelephone(suppContactInfo.get(order.getSupplierId()).getMobile());
        }
        orderInfo.setStockWarehouseType(StockTypeEnum.DEFAULT.getValue());
        // 代配送标识，院区
        order.setSuppDeliveryProxyLabels(suppIdDeliveryProxyLabel.get(masterSearch.getFsuppid()));
        List<OrderExtraDTO> orderExtraDTOList = orderIdExtraMap.get(masterSearch.getId());
        if(CollectionUtils.isNotEmpty(orderExtraDTOList)){
            Map<Integer, String> extraKeyValueMap = DictionaryUtils.toMap(orderExtraDTOList, OrderExtraDTO::getExtraKey, OrderExtraDTO::getExtraValue);
            // 自定义分类标签
            order.setSelfDefCategory(extraKeyValueMap.get(OrderExtraEnum.SELF_DEFINED_CATEGORY.getValue()));
            // 采购类型
            order.setPurchaseType(extraKeyValueMap.get(OrderExtraEnum.PURCHASE_TYPE.getValue()));
            // 是否试用订单
            order.setTrialOrder(CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.IS_TRIAL_ORDER.getValue())));
            order.setRemark(extraKeyValueMap.get(OrderExtraEnum.REMARK.getValue()));
            orderInfo.setStatementWayId(extraKeyValueMap.get(OrderExtraEnum.STATEMENT_WAY_ID.getValue()));
            orderInfo.setSuppNeedFillBatchesData(CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue())));
            orderInfo.setEachProductEachCode(CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue())));
            // 现货仓标识
            String stockWarehouseType = extraKeyValueMap.getOrDefault(OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue(), String.valueOf(StockTypeEnum.DEFAULT.getValue()));
            orderInfo.setStockWarehouseType(Integer.valueOf(stockWarehouseType));
            // 没有获取到，默认就是正常验收
            orderInfo.setAcceptanceWay(extraKeyValueMap.getOrDefault(OrderExtraEnum.ACCEPTANCE_WAY.getValue(), OrderAcceptanceWayEnum.NORMAL.value.toString()));
            // 广州医科大学订单返回资产属性
            if (OrgEnum.GUANG_ZHOU_YI_KE_DA_XUE.getValue() == order.getOrgId()){
                String orderAttrStr = extraKeyValueMap.get(OrderExtraEnum.ORDER_ATTR.getValue());
                order.setOrderAttr(StringUtils.isEmpty(orderAttrStr) ? null : Integer.valueOf(orderAttrStr));
            }
            order.setOuterSuppStatus(extraKeyValueMap.get(OrderExtraEnum.OUTER_SUPP_STATUS.getValue()));
            order.setLackOfGoods(CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.LACK_OF_GOODS.getValue())));
            this.constructAcceptApprovalHint(order, orderListRequireInfo);
            orderInfo.setDockingErrorHint(extraKeyValueMap.get(OrderExtraEnum.ORDER_LIST_DOCKING_ERROR_HINT.getValue()));
            if(OrgEnum.JI_NAN_DA_XUE.getValue() == masterSearch.getFuserid() && OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(masterSearch.getFundStatus())){
                // 暨大自结算
                if(extraKeyValueMap.get(OrderExtraEnum.REPORT_EXPENSE_STATUS.getValue()) != null){
                    // 已完成报账，展示标签。没完成就展示按钮
                    orderInfo.getShowTagList().add(OrderListTagEnum.COMPLETE_REIMBURSEMENT.getTagName());
                }else if(OrderStatusEnum.WaitingForStatement_1.getValue().equals(masterSearch.getStatus()) ||
                        (OrderStatusEnum.Statementing_1.getValue().equals(masterSearch.getStatus()) && StatementStatusEnum.WaitingToPay.getStatus().equals(masterSearch.getStatementStatus()))){
                    orderInfo.getShowButtonList().add(OrderButtonTypeEnum.COMPLETE_REPORT_EXPENSE);
                }
            }
            // 填充订单标签
            this.fillOrderTag(orderInfo, extraKeyValueMap);
        }

        // 经费卡信息
        List<OrderFundcardVO> fundcardVOList = orderIdFundCardVoMap.get(masterSearch.getId());
        if(CollectionUtils.isNotEmpty(fundcardVOList)){
            // 为了让前端少改，先保留这个一张卡对象的赋值，目前只有中科大附一和中大附一用到。--这个对象会被订单详情的处理覆盖
            order.setFundCard(fundcardVOList.get(0));
            // 自定义打印开始使用，所有经费数据
            order.setFundCardList(fundcardVOList);
            order.setFundCardIdList(New.list());
            for(OrderFundcardVO card : fundcardVOList){
                order.getFundCardIdList().add(card.getCardId());
                if(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode().equals(orgCode) && "2".equals(card.getFundType())){
                    //中大附一 经费卡类型为2的订单，不展示经费状态
                    order.setFundStatus(OrderFundStatusEnum.UN_FREEZE.value);
                }
            }
        }

        // 设置结算单状态
        OrderStatementBriefInfoBO statementBriefInfo = this.getStatementBriefInfo(masterSearch, orgCode, statementIdResultMapping);
        if (statementBriefInfo != null) {
            order.setStatementStatus(statementBriefInfo.getStatus());
            order.setSettleCreateDate(statementBriefInfo.getBalanceDate());
            order.setSettleFinishDate(statementBriefInfo.getEndDate());
        }
        //验收权限判断, 验收按钮显示逻辑优化
        Integer acceptanceCheck = this.showAcceptanceValue(accessCodeDeptIdMap, masterSearch, confAccept, request);
        order.setAcceptance(acceptanceCheck);
        this.setAcceptButtonStatus(order, masterSearch, acceptanceCheck, orderListRequireInfo, orderIdAddressMap.get(masterSearch.getId()), fundcardVOList);

        //能否提交入库
        order.setCanInbound(this.canInbound(masterSearch, permitWarehouseDeptIdList));
        //是否能申请入库()
        order.setCanSubmitWarehouse(this.getSubmitPermissionValue(masterSearch, permitWarehouseDeptIdList, useNewWarehouseSystem));
        //能否取消
        order.setCanCancel(curUserId.equals(masterSearch.getFbuyerid()) ? 0 : 1);
        //是否可以打印出入库单
        order.setCanPrintInAndOutWarehouse(this.canPrintInAndOutWarehouse(masterSearch));

        // 物流信息
        OrderDeliveryInfoDTO orderDeliveryInfoDTO = orderIdDeliveryInfoMap.get(order.getId());
        if(orderDeliveryInfoDTO != null){
            // 订单详情会再次覆盖
            orderInfo.setOrderLogisticsInfo(new OrderLogisticsInfoVO().setSubscribeId(orderDeliveryInfoDTO.getSubscribeId()));
        }

        //计算原总价  显示
        String originalAmountString = this.calculateOriginalTotalPrice(order.getOrderType(), order.getTotalPrice(), orderDetailList);
        order.setBeforeModifyPriceAll(originalAmountString);
        // 计算优惠(下单时候的优惠，不考虑退货后的结果。如果不显示原价，这里为0)
        BigDecimal originalAmount = new BigDecimal(originalAmountString);
        BigDecimal discountAmount = originalAmount.subtract(BigDecimal.valueOf(masterSearch.getForderamounttotal()));
        order.setDiscountAmount(discountAmount.compareTo(BigDecimal.ZERO) > 0 ? discountAmount : BigDecimal.ZERO);

        //超时订单判断-兼容与区分全部、验收、结算超时(注意特殊业务：退货中不显示超时)
        boolean acceptOvertime = orderOvertimeBO.getAcceptOvertimeOrderList() != null && orderOvertimeBO.getAcceptOvertimeOrderList().contains(order.getId());
        boolean balanceOvertime = orderOvertimeBO.getBalanceOvertimeOrderList() != null && orderOvertimeBO.getBalanceOvertimeOrderList().contains(order.getId());
        Boolean overtimeByGoodsReturn = this.verificationOrderIfTimeOut(orderDetailList);
        order.setOverTimeFlag((acceptOvertime || balanceOvertime) && overtimeByGoodsReturn);
        order.setOverTimeAcceptance(acceptOvertime && overtimeByGoodsReturn);
        order.setOverTimeBalance(balanceOvertime && overtimeByGoodsReturn);

        // 评价状态
        if (orderIdCommentStatusMap.get(masterSearch.getId()) != null) {
            order.setCommentStatus(orderIdCommentStatusMap.get(masterSearch.getId()).intValue());
        }
        // 对接状态docking
        if (orderNoDockStatusMap != null && !Objects.equals(orderNoDockStatusMap.size(),0)) {
            DockingExtra dockingExtra = orderNoDockStatusMap.get(masterSearch.getForderno());
            if (dockingExtra != null) {
                order.setDockingStatus(dockingExtra.getStatusextra());
                order.setDockingFailReason(dockingExtra.getMemo());
            }
        }

        if(orderNoPushEventStatusMap != null){
            List<OrderPushEventStatusVO> pushEventStatusVOList = orderNoPushEventStatusMap.get(masterSearch.getForderno());
            order.setOrderPushEventStatusList(pushEventStatusVOList);
            // 江西中医附院，返回提交报销单状态
            if (Objects.equals(OrgEnum.JIANG_XI_ZHONG_YI_YAO_DA_XUE_FU_SHU_YI_YUAN.getValue(), order.getOrgId())) {
                if (CollectionUtils.isNotEmpty(pushEventStatusVOList)) {
                    OrderPushEventStatusVO reimbursementEvent = pushEventStatusVOList.stream()
                            .filter(event -> Objects.equals(OrderPushEventEnum.BUYER_APPLY_REIMBURSEMENT.getValue(), event.getOrderPushEventType()))
                            .findFirst()
                            .orElse(null);
                    if (Objects.nonNull(reimbursementEvent)) {
                        orderInfo.setSubmitExpenseStatus(reimbursementEvent.getOrderPushEventStatus());
                    }
                }
            }
        }
        if(orderIdCurrentAcceptApproveLevelMap != null){
            AcceptApproveInfoDTO acceptApproveInfoDTO = orderIdCurrentAcceptApproveLevelMap.get(masterSearch.getId());
            if (Objects.nonNull(acceptApproveInfoDTO)) {
                order.setAcceptApproveLevel(acceptApproveInfoDTO.getCurrentLevel());
            }
            AcceptApprovalStatusVO acceptApprovalStatusVO = getAcceptApprovalStatusInfo(masterSearch, acceptApproveInfoDTO);
            orderInfo.setAcceptApprovalStatusVO(acceptApprovalStatusVO);
        }
        // 存在经费释放失败的订单设置释放失败原因，若不存在保持为null
        if (orderIdMasterMap != null) {
            OrderMasterDO orderMasterDO = orderIdMasterMap.get(masterSearch.getId());
            if(orderMasterDO != null){
                order.setFailedReason(orderMasterDO.getFailedReason());
            }
        }

        // 是否展示撤销入库按钮
        orderInfo.setShowCancelReceipt(showCancelReceiptCtrl &&
                (InventoryStatusEnum.COMPLETE.getCode().equals(order.getInventoryStatus())
                        || InventoryStatusEnum.NOT_INBOUND.getCode().equals(order.getInventoryStatus())));

        // 代配送是否开启，代配送来源
        OrderAddressDTO orderAddressDTO = orderIdAddressMap.get(masterSearch.getId());
        if(orderAddressDTO != null){
            // 属于代配送的类型集合
            order.setDeliveryProxyOn(DeliveryTypeEnum.PROXY_DELIVERY_LIST.contains(orderAddressDTO.getDeliveryType()));
            order.setDeliveryProxySourceType(orderAddressDTO.getProxySourceType());
            order.setDeliveryType(orderAddressDTO.getDeliveryType());
            if(orderIdCancelCxDelPxyLogMap.get(masterSearch.getId()) != null){
                order.setCancelDeliveryProxyReason(orderIdCancelCxDelPxyLogMap.get(masterSearch.getId()).getReason());
            }
            orderInfo.setDeliveryStatus(orderAddressDTO.getDeliveryStatus());
        }

        if(orderIdWarehouseRejectReasonMap != null){
            order.setWarehouseRejectReason(orderIdWarehouseRejectReasonMap.get(masterSearch.getId()));
        }

        // 合同状态
        if (orderIdContractStatusMap.get(masterSearch.getId()) != null) {
            orderInfo.setOrderContractStatus(orderIdContractStatusMap.get(masterSearch.getId()));
        }

        // 新版合同
        if(Objects.nonNull(orderNoContractInfoMap)){
            List<OrderContractInfoVO> contractInfoVOS = orderNoContractInfoMap.get(masterSearch.getForderno());
            orderInfo.setOrderContractInfoVOList(CollectionUtils.isNotEmpty(contractInfoVOS) ? contractInfoVOS : New.emptyList());
        }

        // 备案信息
        OrderConfirmForTheRecordDO confirmRecord = orderIdConfirmRecordMap.get(masterSearch.getId());
        if (confirmRecord != null) {
            OrderConfirmEnum orderConfirmEnum = DangerousConstant.transformType(masterSearch.getStatus(), confirmRecord);
            orderInfo.setConfirmForTheRecord(orderConfirmEnum.getValue());
        }

        // hms额外逻辑
        if (isHms) {
            // 线下供应商信息(仅hms，否则不检索以提高运行速度，detail处也需要查询）
            order.setOfflineInfo(orderIdOfflineInfoMap.get(masterSearch.getId()));
            if (confirmRecord != null && OrderConfirmEnum.NEED_CONFIRM_AND_PICS.getValue().equals(confirmRecord.getType())) {
                order.setConfirmPics(confirmRecord.getPics());
                order.setConfirmAddPics(confirmRecord.getAddPics());
            }

            // 抽检按钮显示:有抽检权限+订单状态在待结算/结算中/已完成+没有抽检过
            boolean showAuditSamplingButton = CollectionUtils.isNotEmpty(accessCodeDeptIdMap.get(ConfigConstant.ORDER_SAMPLING_INSPECTION_CODE)) && accessCodeDeptIdMap.get(ConfigConstant.ORDER_SAMPLING_INSPECTION_CODE).contains(masterSearch.getFbuydepartmentid());
            showAuditSamplingButton = showAuditSamplingButton && (OrderStatusEnum.WaitingForStatement_1.getValue().equals(masterSearch.getStatus()) || OrderStatusEnum.Statementing_1.getValue().equals(masterSearch.getStatus()) || OrderStatusEnum.Finish.getValue().equals(masterSearch.getStatus()));
            showAuditSamplingButton = showAuditSamplingButton && orderListRequireInfo.getOrderNoAuditSamplingMap().get(order.getOrderNo()) == null;
            if(showAuditSamplingButton){
                orderInfo.getShowButtonList().add(OrderButtonTypeEnum.AUDIT_SAMPLING);
            }
        }

        if (orderNoPicMap != null) {
            List<OrderPic> orderPics = orderNoPicMap.get(order.getOrderNo());
            if (orderPics != null) {
                List<OrderPicVO> collect = orderPics.stream().map(it -> new OrderPicVO().setUrl(SYSUOrderService.joinRecPicUrl(order.getOrgId(), it.getPic())).setTime(it.getCreateTime().getTime())).sorted(Comparator.comparing(OrderPicVO::getTime)).collect(toList());
                orderInfo.setReceiveImages(collect);
            }
        }

        // 处理上传的文件信息
        Map<Integer, List<OrderUploadFileDTO>> orderUploadFileDTOMap = orderIdUploadFileMap.get(order.getId());
        if (MapUtils.isNotEmpty(orderUploadFileDTOMap)) {
            // 验收附件
            List<OrderUploadFileDTO> acceptanceAttachment = orderUploadFileDTOMap.get(FileBusinessTypeEnum.ACCEPTANCE_ATTACHMENT.getCode());
            orderInfo.setAttachmentList(CollectionUtils.isNotEmpty(acceptanceAttachment) ?
                    FileTranslator.orderUploadFileDTOList2OrderAttachmentVOList(acceptanceAttachment) : New.emptyList());

            // 验收视频
            List<OrderUploadFileDTO> acceptanceVideo = orderUploadFileDTOMap.get(FileBusinessTypeEnum.ACCEPTANCE_VIDEO.getCode());
            orderInfo.setOrderVideoAttachmentList(CollectionUtils.isNotEmpty(acceptanceVideo) ?
                    FileTranslator.orderUploadFileDTOList2OrderAttachmentVOList(acceptanceVideo) : New.emptyList());

            // 支付记录
            List<OrderUploadFileDTO> paymentRecord = orderUploadFileDTOMap.get(FileBusinessTypeEnum.PAYMENT_RECORD.getCode());
            orderInfo.setPaymentRecordList(CollectionUtils.isNotEmpty(paymentRecord) ?
                    FileTranslator.orderUploadFileDTOList2OrderAttachmentVOList(paymentRecord) : New.emptyList());
        }
        // 商品关联图片信息
        if (MapUtils.isNotEmpty(orderDetailAcceptancePicsMap)) {
            List<AcceptPictureDTO> acceptPictureDTOList = orderDetailAcceptancePicsMap.get(masterSearch.getId());
            orderInfo.setDetailPictureDTOList(CollectionUtils.isNotEmpty(acceptPictureDTOList) ? acceptPictureDTOList : New.emptyList());
        }

        // 商品关联附件信息
        if (MapUtils.isNotEmpty(orderDetailAcceptanceFilesMap)) {
            List<AcceptAttachmentDTO> acceptAttachmentDTOList = orderDetailAcceptanceFilesMap.get(masterSearch.getId());
            orderInfo.setDetailAttachmentDTOList(CollectionUtils.isNotEmpty(acceptAttachmentDTOList) ? acceptAttachmentDTOList : New.emptyList());
        }

        // 处理线下单账户信息
        if (MapUtils.isNotEmpty(orderIdAccountTypeMap)) {
            orderInfo.setAccountType(orderIdAccountTypeMap.get(masterSearch.getId()));
        }

        orderInfo.setOldFlag(oldDateConfigBO != null && oldDateConfigBO.getIsOldOrder(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, order.getOrderDate()), order.getFundStatus(), order.getSpecies()));

        if (buyerDepartmentIdESignConfigMap != null) {
            order.setElectronicSignConfigVO(buyerDepartmentIdESignConfigMap.get(order.getBuyDepartmentId()));
        }

        if(uploadedInvoiceOrderList != null){
            orderInfo.setUploadInvoiceComplete(uploadedInvoiceOrderList.contains(order.getId()));
        }

        // 判断是否是整单退货
        boolean isAllReturn = false;
        List<GoodsReturn> goodsReturns = goodsReturnMapper.findByOrderId(order.getId());
        goodsReturns = goodsReturns.stream().filter(goodsReturn -> !GoodsReturnStatusEnum.CANCEL_REQUEST.getCode().equals(goodsReturn.getGoodsReturnStatus())).collect(toList());
        if(CollectionUtils.isNotEmpty(goodsReturns) && goodsReturns.size() == 1) {
            isAllReturn = true;
            List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOList = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturns.get(0).getGoodsReturnDetailJSON());
            Map<Integer, Integer> quantityMap = new HashMap<>(orderDetailList.size());
            for (GoodsReturnInfoDetailVO detailVO : goodsReturnInfoDetailVOList) {
                Integer detailId = Integer.parseInt(detailVO.getDetailId());
                Integer quantity = quantityMap.get(detailId);
                if (quantity == null) {
                    quantity = detailVO.getQuantity().intValue();
                } else {
                    quantity = quantity + detailVO.getQuantity().intValue();
                }
                quantityMap.put(detailId, quantity);
            }
            for (OrderDetailVO orderDetailVO : orderDetailList) {
                Integer returnQuantity = quantityMap.get(orderDetailVO.getId());
                if (returnQuantity == null || orderDetailVO.getQuantity().intValue() != returnQuantity) {
                    isAllReturn = false;
                    break;
                }
            }
        }
        orderInfo.setAllReturn(isAllReturn);
        // 重新解冻按钮-整单退货配置且经费解冻失败时展示
        List<String> allRetrunConfigList = baseConfigCodeValueMap.get(ConfigCodeEnum.ORDER_RETURN_ONLY_WHOLE.name());
        String isAllReturnConfig = CollectionUtils.isNotEmpty(allRetrunConfigList) ? allRetrunConfigList.get(0) : ConfigCodeEnum.ORDER_RETURN_ONLY_WHOLE.getValue();
        boolean isAllReturnConfigFlag = CommonValueUtils.parseNumberStrToBoolean(isAllReturnConfig);
        boolean showReUnfreeze = isAllReturnConfigFlag && OrderFundStatusEnum.ThrawFailed.value.equals(orderInfo.getOrder().getFundStatus());
        orderInfo.setShowReUnFreeze(showReUnfreeze);

        // 整单退货标识，返回是否限制只能整单退货，先判断一般的OMS配置，如果否再判断是否对接限制
        if (!isAllReturnConfigFlag) {
            isAllReturnConfigFlag = OmsDockingConfigValueEnum.WHOLE_ORDER.name().equals(dockingConfig.getOrderDockingConfigDTO().getReturnGoodsRange());
            isAllReturnConfigFlag = isAllReturnConfigFlag
                    && enableDockingConfigOrderIdList.contains(masterSearch.getId());
        }
        orderInfo.setLimitOnlyWholeReturn(isAllReturnConfigFlag);
        return orderInfo;
    }

    /**
     * 填充订单标签
     */
    private void fillOrderTag(OrderInfoVO orderInfo, Map<Integer, String> extraKeyValueMap) {
        if (Objects.isNull(orderInfo)) {
            return;
        }
        Integer orgId = orderInfo.getOrder().getOrgId();
        List<String> showTagList = orderInfo.getShowTagList();
        if (Objects.isNull(showTagList)) {
            showTagList = New.list();
        }

        String orderTagJsonArray = extraKeyValueMap.get(OrderExtraEnum.ORDER_TAG.getValue());
        List<Integer> orderTagList = parseJsonList(orderTagJsonArray);
        // 四川华西定制
        if (Objects.equals(OrgEnum.SI_CHUAN_DA_XUE_HUA_XI_YI_YUAN.getValue(), orgId)) {
            if (CollectionUtils.isEmpty(orderTagList)) {
                return;
            }
            for (Integer tag : orderTagList) {
                OrderTagEnum orderTagEnum = OrderTagEnum.getByValue(tag);
                if (Objects.nonNull(orderTagEnum)) {
                    showTagList.add(orderTagEnum.getDesc());
                }
            }
        }
    }

    /**
     * 解析 JSON 成 List<String>
     */
    private List<Integer> parseJsonList(String json) {
        if (StringUtils.isBlank(json)) {
            return New.emptyList();
        }
        List<Integer> list = JsonUtils.parseList(json, Integer.class);
        return Objects.nonNull(list) ? list : New.emptyList();
    }

    /**
     * 批量获取新版合同信息
     *
     * @param orderNoList 订单号列表
     * @return 订单号到合同信息列表的映射
     */
    public Map<String, List<OrderContractInfoVO>> getOrderNewContractInfoMap(List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return New.emptyMap();
        }

        // 批量查询订单合同信息
        OrderContractQueryDTO orderContractQueryDTO = new OrderContractQueryDTO();
        orderContractQueryDTO.setOrderNoList(orderNoList);
        List<OrderContractInfoVO> contractInfoVOList = orderContractClient.getOrderContractInfo(orderContractQueryDTO);

        if (CollectionUtils.isEmpty(contractInfoVOList)) {
            return New.emptyMap();
        }

        // 按订单号分组
        Map<String, List<OrderContractInfoVO>> orderNoContractInfoMap = New.mapWithCapacity(orderNoList.size());
        for (OrderContractInfoVO contractInfoVO : contractInfoVOList) {
            String orderNo = contractInfoVO.getOrderNo();
            if (StringUtils.isBlank(orderNo)) {
                continue;
            }
            orderNoContractInfoMap.computeIfAbsent(orderNo, k -> New.list()).add(contractInfoVO);
        }
        return orderNoContractInfoMap;
    }


    /**
     * 批量查询订单验收图片
     * @param orderIds 订单ID列表
     * @return 订单ID到验收图片列表的映射
     */
    public Map<Integer, List<AcceptPictureDTO>> getOrderDetailAcceptancePicsMap(List<Integer> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return New.emptyMap();
        }

        // 批量查询验收图片
        List<OrderDetailAcceptancePicDTO> acceptancePicDOList = orderAcceptCommentClient.listDetailAcceptancePicByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(acceptancePicDOList)) {
            return New.emptyMap();
        }

        // 按订单ID分组
        Map<Integer, List<OrderDetailAcceptancePicDTO>> orderPicMap = DictionaryUtils.groupBy(acceptancePicDOList, OrderDetailAcceptancePicDTO::getOrderId);

        // 构建返回结果
        Map<Integer, List<AcceptPictureDTO>> resultMap = New.mapWithCapacity(orderIds.size());

        for (Map.Entry<Integer, List<OrderDetailAcceptancePicDTO>> entry : orderPicMap.entrySet()) {
            Integer orderId = entry.getKey();
            List<OrderDetailAcceptancePicDTO> picList = entry.getValue();

            // 处理验收图片
            List<AcceptPictureDTO> acceptPictureDTOList = New.list();
            if (CollectionUtils.isNotEmpty(picList)) {
                Map<String, List<OrderDetailAcceptancePicDTO>> picGroupMap = DictionaryUtils.groupBy(picList, OrderDetailAcceptancePicDTO::getUrl);
                for (Map.Entry<String, List<OrderDetailAcceptancePicDTO>> picEntry : picGroupMap.entrySet()) {
                    List<OrderDetailAcceptancePicDTO> picDOS = picEntry.getValue();
                    String url = picEntry.getKey();
                    String fileName = picDOS.get(0).getFileName();

                    AcceptPictureDTO acceptPictureDTO = new AcceptPictureDTO();
                    List<Integer> orderDetailIdList = picDOS.stream()
                            .map(OrderDetailAcceptancePicDTO::getDetailId)
                            .filter(Objects::nonNull)
                            .collect(toList());
                    acceptPictureDTO.setPictureDTO(new PictureDTO(url, fileName));
                    acceptPictureDTO.setOrderDetailIdList(orderDetailIdList);
                    acceptPictureDTOList.add(acceptPictureDTO);
                }
            }

            resultMap.put(orderId, acceptPictureDTOList);
        }

        return resultMap;
    }

    /**
     * 批量查询订单验收附件
     * @param orderIds 订单ID列表
     * @return 订单ID到验收附件列表的映射
     */
    public Map<Integer, List<AcceptAttachmentDTO>> getOrderDetailAcceptanceFilesMap(List<Integer> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return New.emptyMap();
        }

        // 批量查询验收附件
        List<OrderDetailAcceptanceFileDTO> acceptanceFileDOList = orderAcceptCommentClient.listDetailAcceptanceFileByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(acceptanceFileDOList)) {
            return New.emptyMap();
        }

        // 按订单ID分组
        Map<Integer, List<OrderDetailAcceptanceFileDTO>> orderFileMap = DictionaryUtils.groupBy(acceptanceFileDOList, OrderDetailAcceptanceFileDTO::getOrderId);

        // 构建返回结果
        Map<Integer, List<AcceptAttachmentDTO>> resultMap = New.mapWithCapacity(orderIds.size());

        for (Map.Entry<Integer, List<OrderDetailAcceptanceFileDTO>> entry : orderFileMap.entrySet()) {
            Integer orderId = entry.getKey();
            List<OrderDetailAcceptanceFileDTO> fileList = entry.getValue();

            // 处理验收附件
            List<AcceptAttachmentDTO> acceptAttachmentDTOList = New.list();
            if (CollectionUtils.isNotEmpty(fileList)) {
                Map<String, List<OrderDetailAcceptanceFileDTO>> fileGroupMap = DictionaryUtils.groupBy(fileList, OrderDetailAcceptanceFileDTO::getUrl);
                for (Map.Entry<String, List<OrderDetailAcceptanceFileDTO>> fileEntry : fileGroupMap.entrySet()) {
                    List<OrderDetailAcceptanceFileDTO> fileDOS = fileEntry.getValue();
                    String url = fileEntry.getKey();
                    String fileName = fileDOS.get(0).getFileName();

                    AcceptAttachmentDTO acceptAttachmentDTO = new AcceptAttachmentDTO();
                    List<Integer> orderDetailIdList = fileDOS.stream()
                            .map(OrderDetailAcceptanceFileDTO::getDetailId)
                            .filter(Objects::nonNull)
                            .collect(toList());
                    acceptAttachmentDTO.setAttachment(new AttachmentDTO(url, fileName));
                    acceptAttachmentDTO.setOrderDetailIdList(orderDetailIdList);
                    acceptAttachmentDTOList.add(acceptAttachmentDTO);
                }
            }

            resultMap.put(orderId, acceptAttachmentDTOList);
        }

        return resultMap;
    }

    /**
     * 获取验收审批流程状态
     *
     * @param masterSearchDTO      订单主表信息
     * @param acceptApproveInfoDTO 验收审批信息
     */
    private AcceptApprovalStatusVO getAcceptApprovalStatusInfo(OrderMasterSearchDTO masterSearchDTO, AcceptApproveInfoDTO acceptApproveInfoDTO) {
        AcceptApprovalStatusVO resultVO = new AcceptApprovalStatusVO();
        resultVO.setAcceptApprovalStatusDesc(StringUtils.EMPTY);
        if (Objects.isNull(masterSearchDTO)) {
            return resultVO;
        }

        if (Objects.nonNull(acceptApproveInfoDTO)) {
            // 1. 订单验收审批中
            if (Objects.equals(masterSearchDTO.getStatus(), OrderStatusEnum.OrderReceiveApproval.getValue())) {
                resultVO.setAcceptApprovalStatus(AcceptApprovalStatusEnum.APPROVING.getCode());
                String desc = StrUtil.format("待{}级审批", NumberChineseFormatter.format(acceptApproveInfoDTO.getCurrentLevel(), false));
                resultVO.setAcceptApprovalStatusDesc(desc);
                resultVO.setAcceptApprovalLevel(acceptApproveInfoDTO.getCurrentLevel());
                return resultVO;
            }
            // 2. 验收审批通过
            Integer completedFlag = 1;
            if (Objects.equals((acceptApproveInfoDTO.getIsComplete()), completedFlag)) {
                resultVO.setAcceptApprovalStatus(AcceptApprovalStatusEnum.PASSED.getCode());
                resultVO.setAcceptApprovalStatusDesc(AcceptApprovalStatusEnum.PASSED.getDesc());
                return resultVO;
            }
        }

        // 找出最新的验收审批日志
        List<OrderLogSearchDTO> logSearchDTOList = masterSearchDTO.getLog();
        if (CollectionUtils.isEmpty(logSearchDTOList)) {
            return resultVO;
        }
        OrderLogSearchDTO latestAcceptanceLog = logSearchDTOList.stream()
                .filter(log -> OrderApprovalEnum.PASS.getValue().equals(log.getApproveStatus()) || OrderApprovalEnum.REJECT.getValue().equals(log.getApproveStatus()))
                .max(Comparator.comparing(OrderLogSearchDTO::getCreationTime)).orElse(null);
        // 3. 验收审批驳回
        if (Objects.nonNull(latestAcceptanceLog) && OrderApprovalEnum.REJECT.getValue().equals(latestAcceptanceLog.getApproveStatus())) {
            resultVO.setAcceptApprovalStatus(AcceptApprovalStatusEnum.REJECTED.getCode());
            resultVO.setAcceptApprovalStatusDesc(AcceptApprovalStatusEnum.REJECTED.getDesc());
            return resultVO;
        }
        return resultVO;
    }

    /**
     * @param orderNoList
     * @return java.util.Map<java.lang.String, java.lang.Integer>
     * @description: 订单-对接状态 对应关系构建
     * @date: 2020/12/3 9:44
     * @author: zengyanru
     */
    private Map<String, DockingExtra> constructOrderNoDockStatusMap(List<String> orderNoList) {
        if (CollectionUtils.isNotEmpty(orderNoList)) {
            List<DockingExtra> dockingExtraList = dockingExtraMapper.findByInfoInAndType(orderNoList, DockingTypeEnum.Order.getValue());
            // 按照store的逻辑是取找到的第一个数
            return dockingExtraList.stream().collect(toMap(DockingExtra::getInfo, Function.identity(),(oldValue,newValue) -> oldValue));
        } else {
            return null;
        }
    }

    /**
     * 获取订单推送状态
     * @param orderNoList 订单号列表
     * @param orgCode 学校机构代码
     * @return 订单-推送状态map
     */
    private Map<String, List<OrderPushEventStatusVO>> constructOrderNoPushEventStatusMap(List<String> orderNoList,String orgCode){
        Map<String, List<OrderPushEventStatusVO>> orderNoPushEventStatusMap = New.mapWithCapacity(orderNoList.size());
        // 同时支持中大办公和江西中医附院
        if (!ZhongShanDaXueBelongUtils.isBanGongByOrgCode(orgCode)
                && !OrgEnum.JIANG_XI_ZHONG_YI_YAO_DA_XUE_FU_SHU_YI_YUAN.getCode().equals(orgCode)) {
            return null;
        }
        OrderEventStatusRequestDTO orderEventStatusRequestDto = new OrderEventStatusRequestDTO();
        orderEventStatusRequestDto.setOrderNoList(orderNoList);
        List<OrderPushEventEnum> orderPushEventList = New.list(OrderPushEventEnum.PUSH_TO_SUPPLIER,OrderPushEventEnum.BUYER_CANCEL_TO_SUPPLIER_WHEN_WAITING_FOR_DELIVERY);
        // 江西中医附院，查询提交报销单推送状态
        if (OrgEnum.JIANG_XI_ZHONG_YI_YAO_DA_XUE_FU_SHU_YI_YUAN.getCode().equals(orgCode)) {
            orderPushEventList = New.list(OrderPushEventEnum.BUYER_APPLY_REIMBURSEMENT);
        }
        orderEventStatusRequestDto.setOrderPushEventEnumList(orderPushEventList);
        // 查询thunder记录的订单推送状态
        List<OrderEventStatusResponseDTO> orderEventStatusResponseDtoList = orderPushEventStatusClient.listEventPushStatus(orderEventStatusRequestDto);
        for(OrderEventStatusResponseDTO orderEventStatusResponseDto : orderEventStatusResponseDtoList){
            OrderPushEventStatusVO orderPushEventStatusVo = new OrderPushEventStatusVO();
            orderPushEventStatusVo.setOrderPushEventType(orderEventStatusResponseDto.getOrderPushEventEnum().getValue());
            orderPushEventStatusVo.setOrderPushEventStatus(orderEventStatusResponseDto.getOrderEventStatusEnum().getValue());
            // 失败时返回原因
            if(OrderEventStatusEnum.FAILED.equals(orderEventStatusResponseDto.getOrderEventStatusEnum())){
                orderPushEventStatusVo.setFailReason(orderEventStatusResponseDto.getFailReason());
            }
            int eventSize = orderPushEventList.size();
            List<OrderPushEventStatusVO> orderPushEventStatusVoList = orderNoPushEventStatusMap.computeIfAbsent(orderEventStatusResponseDto.getOrderNo(), k -> New.listWithCapacity(eventSize));
            orderPushEventStatusVoList.add(orderPushEventStatusVo);
        }
        return orderNoPushEventStatusMap;
    }

    /**
     * 获取验收审批信息，转换为订单id->当前验收审批等级的map
     * @param orderIdList 订单id
     * @return 订单id->当前验收审批等级的map
     */
    private Map<Integer, AcceptApproveInfoDTO> constructOrderIdCurrentAcceptApproveLevelMap(List<Integer> orderIdList) {
        List<AcceptApproveInfoDTO> acceptApproveInfoDTOList = acceptApprovalClient.getAcceptApproveInfo(orderIdList);
        return CollectionUtils.isEmpty(acceptApproveInfoDTOList) ? New.emptyMap() : DictionaryUtils.toMap(acceptApproveInfoDTOList, AcceptApproveInfoDTO::getOrderId, dto -> dto);
    }

    /**
     * @param masterSearch
     * @param confAccept
     * @param request
     * @return java.lang.Integer
     * @description: 确定是否显示验收按钮
     * @date: 2020/12/3 9:45
     * @author: zengyanru
     */
    private Integer showAcceptanceValue(Map<String, List<Integer>> codeAuthDeptIdMap, OrderMasterSearchDTO masterSearch, OrderAcceptWayEnum confAccept, OrderListRequest request) {
        // 是否有服务/非服务类商品
        boolean withService = false;
        boolean withNonService = false;
        // 判断订单是否包含 管制/非管制 类危化品
        boolean withRegulatoryDangerous = false;
        boolean withNonRegulatoryDangerous = false;
        for(OrderDetailSearchDTO detail : masterSearch.getOrderDetail()){
            if(Objects.equals(CategoryConstant.SCIENCE_SERVICE_ID, detail.getFirstCategoryId())){
                withService = true;
            }else {
                withNonService = true;
            }
            // 判断危化品类型
            if (Objects.equals(CategoryConstant.DANGEROUS_ID, detail.getFirstCategoryId())) {
                if (Objects.equals(RegulatoryTypeEnum.REGULATORY.getRegulatoryType(), detail.getRegulatoryType())) {
                    withRegulatoryDangerous = true;
                } else if (Objects.equals(RegulatoryTypeEnum.UNREGULATORY.getRegulatoryType(), detail.getRegulatoryType())) {
                    withNonRegulatoryDangerous = true;
                }
            }
        }
        if (Objects.equals(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN.getValue(), masterSearch.getFuserid())) {
            // 中大五院定制， 科研服务类订单，无视权限，谁都能验收
            if (withService) {
                return OrderAcceptPermitEnum.CAN_ACCEPT.getValue();
            }
        }
        if(OrgEnum.GUANG_ZHOU_FU_DA_ZHONG_LIU_YI_YUAN.getValue() == masterSearch.getFuserid()
                && OrderTypeEnum.BID_ORDER.getCode().equals(masterSearch.getOrderType())){
            // 广州复大医疗有限公司复大肿瘤医院，不是采购单，则不读取验收方式和验收权限，采购人仅可验收自己的订单
            return request.getFbuyerId().equals(masterSearch.getFbuyerid()) ? OrderAcceptPermitEnum.CAN_ACCEPT.getValue() : OrderAcceptPermitEnum.CAN_NOT_ACCEPT.getValue();
        }
        // 取验收权限
        Set<Integer> authDeptIdSet = New.set();

        // 订单验收权限 -可以验收所有类型的订单
        if(CollectionUtils.isNotEmpty(codeAuthDeptIdMap.get(ConfigConstant.ORDER_ACCEPTANCE))){
            authDeptIdSet.addAll(codeAuthDeptIdMap.get(ConfigConstant.ORDER_ACCEPTANCE));
        }
        // 服务类订单验收权限
        if(withService && CollectionUtils.isNotEmpty(codeAuthDeptIdMap.get(BuyerCenterAccessConstant.SERVICE_ORDER_ACCEPTANCE))){
            authDeptIdSet.addAll(codeAuthDeptIdMap.get(BuyerCenterAccessConstant.SERVICE_ORDER_ACCEPTANCE));
        }
        // 非服务类订单验收权限
        if(withNonService && CollectionUtils.isNotEmpty(codeAuthDeptIdMap.get(BuyerCenterAccessConstant.NON_SERVICE_ORDER_ACCEPTANCE))){
            authDeptIdSet.addAll(codeAuthDeptIdMap.get(BuyerCenterAccessConstant.NON_SERVICE_ORDER_ACCEPTANCE));
        }
        // 管制类危化品订单验收权限
        if(withRegulatoryDangerous && CollectionUtils.isNotEmpty(codeAuthDeptIdMap.get(ConfigConstant.REGULATORY_ORDER_ACCEPTANCE))){
            authDeptIdSet.addAll(codeAuthDeptIdMap.get(ConfigConstant.REGULATORY_ORDER_ACCEPTANCE));
        }
        // 非管制类危化品订单验收权限
        if(withNonRegulatoryDangerous && CollectionUtils.isNotEmpty(codeAuthDeptIdMap.get(ConfigConstant.NON_REGULATORY_ORDER_ACCEPTANCE))){
            authDeptIdSet.addAll(codeAuthDeptIdMap.get(ConfigConstant.NON_REGULATORY_ORDER_ACCEPTANCE));
        }

        if (CollectionUtils.isNotEmpty(authDeptIdSet) && authDeptIdSet.contains(masterSearch.getFbuydepartmentid())) {
            // 自行验收--表示可以自行验收也可以验收其他人的单
            Boolean selfAcceptance = (OrderAcceptWayEnum.SELF_ACCEPTANCE.value).equals(confAccept.value);
            // 交叉验收--只能验收其他人的单
            Boolean crossAcceptance = (OrderAcceptWayEnum.CROSS_ACCEPTANCE.value).equals(confAccept.value) && !request.getFbuyerId().equals(masterSearch.getFbuyerid());
            if (selfAcceptance || crossAcceptance) {
                return OrderAcceptPermitEnum.CAN_ACCEPT.getValue();
            } else {
                return OrderAcceptPermitEnum.CAN_NOT_ACCEPT.getValue();
            }
        }
        return OrderAcceptPermitEnum.CAN_NOT_ACCEPT.getValue();
    }

    /**
     * 验收按钮显示逻辑优化
     * @param masterSearch
     * @param acceptanceCheck
     * @return
     */
    private void setAcceptButtonStatus(OrderMasterVO order, OrderMasterSearchDTO masterSearch, Integer acceptanceCheck, OrderListRequireInfoBO orderListRequireInfo, OrderAddressDTO orderAddressDTO, List<OrderFundcardVO> fundcardVOList) {
        // 1.特殊单位逻辑与不显示逻辑(非待收货状态的话)
        if (!OrderStatusEnum.WaitingForReceive.getValue().equals(masterSearch.getStatus())) {
            order.setAcceptButtonStatus(OrderAcceptButtonEnum.NOT_SHOW.getValue());
            return;
        }
        // 2.特殊单位，非指定接收人按钮置灰
        String userGuid = orderListRequireInfo.getCurUserGuid();
        // 管制品，非指定接收人则置灰
        boolean isRegulatoryOrder = masterSearch.getOrderDetail().stream().anyMatch(detail -> RegulatoryTypeEnum.REGULATORY.getRegulatoryType().equals(detail.getRegulatoryType()));
        if (isRegulatoryOrder) {
            // 定制化验收人guid为null则为没配置，不校验
            List<String> customAcceptorGuid = CustomAcceptorConstant.getRegulatoryAcceptorUserGuid(orderListRequireInfo.getOrgCode());
            if(customAcceptorGuid != null && !customAcceptorGuid.contains(userGuid)){
                order.setAcceptButtonStatus(OrderAcceptButtonEnum.SHOW_GRAY.getValue());
                return;
            }
        }
        // 江西肿瘤定制，院内地址标签且不为动物类/服务类的，需要校验验收人
        boolean containsExpAnimalOrService = masterSearch.getOrderDetail().stream().anyMatch(detail -> CategoryConstant.EXPERIMENT_ANIMAL_ID == detail.getFirstCategoryId()
                || CategoryConstant.SCIENCE_SERVICE_ID.equals(detail.getFirstCategoryId()));
        if(!containsExpAnimalOrService && OrgEnum.JIANG_XI_ZHONG_LIU.getValue() == masterSearch.getFuserid() && orderAddressDTO != null && "院内实验用".equals(orderAddressDTO.getLabel())){
            if(!CustomAcceptorConstant.JIANG_XI_ZHONG_LIU_EXP_ANIMAL_OR_SERVICE_CUSTOM_ACCEPT_MAN_ID.contains(orderListRequireInfo.getCurUserId())){
                order.setAcceptButtonStatus(OrderAcceptButtonEnum.SHOW_GRAY.getValue());
                order.setAcceptButtonDisableHint(LocaleUtils.translate(ExecptionMessageEnum.INSPECTION_REQUIREMENTS_UNIT));
                return;
            }
        }

        if(OrgEnum.SHEN_ZHEN_YI_XUE_KE_XUE_YUAN.getValue() == masterSearch.getFuserid()){
            boolean isFundManager = fundcardVOList.stream().anyMatch(card->CollectionUtils.isNotEmpty(card.getFundManagerUserIds()) && card.getFundManagerUserIds().contains(orderListRequireInfo.getCurUserId()));
            if(isFundManager){
                order.setAcceptButtonStatus(OrderAcceptButtonEnum.SHOW_GRAY.getValue()).setAcceptButtonDisableHint("单位要求，订单的项目负责人不能为验收人，请联系同课题组其他成员验收");
                return;
            }
        }

        OrgDockingConfigDTO dockingConfig = orderListRequireInfo.getDockingConfig();
        boolean enableNewDockingConfig = orderListRequireInfo.getEnableDockingConfigOrderIdList().contains(masterSearch.getId());
        if(enableNewDockingConfig && OmsDockingConfigValueEnum.ACCEPT_IN_EXTERNAL.name().equals(dockingConfig.getOrderDockingConfigDTO().getOrderSyncStatusAccept())){
            if (offlineNoPushOrgSet.contains(masterSearch.getFuserid()) && Objects.equals(SpeciesEnum.OFFLINE.getValue(), masterSearch.getSpecies())) {
                // 对接不推送线下单的单位，允许验收
                order.setAcceptButtonStatus(OrderAcceptButtonEnum.SHOW_NORMAL.getValue());
                return;
            }
            order.setAcceptButtonStatus(OrderAcceptButtonEnum.SHOW_GRAY.getValue());
            String hint = dockingConfig.getOrderDockingConfigDTO().getOrderDisableReceiveButtonHint();
            hint = StringUtils.isEmpty(hint) ? null : hint;
            order.setAcceptButtonDisableHint(hint);
            return;
        }

        // 3.验收逻辑
        if (OrderAcceptPermitEnum.CAN_ACCEPT.getValue().equals(acceptanceCheck)) {
            order.setAcceptButtonStatus(OrderAcceptButtonEnum.SHOW_NORMAL.getValue());
            return;
        }
        // 置灰时的默认文案
        order.setAcceptButtonStatus(OrderAcceptButtonEnum.SHOW_GRAY.getValue());
        order.setAcceptButtonDisableHint("单位要求，采购经办人不能同时为验收人，请联系同课题组其他成员验收");
    }

    /**
     * 获取验收或结算的超时订单ID列表
     *
     * @param orgId 单位ID
     * @param codeBaseConfigMap 基础配置映射表，key为配置项名称，value为配置项列表
     * @return 包含验收和结算超时订单ID的OrderOvertimeBO对象
     * <AUTHOR>
     * @date 2020/12/3 9:45
     */
    private OrderOvertimeBO findOvertimeOrderIdList(List<OrderMasterSearchDTO> masterSearchList, Integer orgId, Map<String, List<BaseConfigDTO>> codeBaseConfigMap) {
        Integer acceptLimitDays = TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getDefaultSet();
        Integer balanceLimitDays = TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getDefaultSet();

        if (MapUtils.isNotEmpty(codeBaseConfigMap)) {
            List<BaseConfigDTO> examineLimitConfigList = codeBaseConfigMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.name());
            List<BaseConfigDTO> balanceLimitConfigList = codeBaseConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.name());

            if (CollectionUtils.isNotEmpty(examineLimitConfigList)) {
                acceptLimitDays = Integer.valueOf(examineLimitConfigList.get(0).getConfigValue());
            }
            if (CollectionUtils.isNotEmpty(balanceLimitConfigList)) {
                balanceLimitDays = Integer.valueOf(balanceLimitConfigList.get(0).getConfigValue());
            }
        }

        // 验收、结算列表
        Set<Integer> acceptOvertimeOrderList = this.getAcceptTimeoutOrderIdList(masterSearchList, acceptLimitDays);
        Set<Integer> balanceOvertimeOrderList = this.getBalanceTimeoutOrderIdList(masterSearchList, balanceLimitDays);
        OrderOvertimeBO overtimeBO = new OrderOvertimeBO();
        overtimeBO.setAcceptOvertimeOrderList(acceptOvertimeOrderList).setBalanceOvertimeOrderList(balanceOvertimeOrderList);
        return overtimeBO;
    }

    /**
     * @param limitDays
     * @return java.util.List<java.lang.Integer>
     * @description: 获取结算超时单id
     * @date: 2020/12/2 16:16
     * @author: zengyanru
     */
    private Set<Integer> getBalanceTimeoutOrderIdList(List<OrderMasterSearchDTO> masterSearchList, Integer limitDays) {
        // 结算
        List<Integer> statusList = New.list(OrderStatusEnum.WaitingForStatement_1.value, OrderStatusEnum.OrderReceiveApproval.value);

        return masterSearchList.stream().
                filter(s -> statusList.contains(s.getStatus())).
                filter(s -> s.getSpecies().equals(ProcessSpeciesEnum.NORMAL.getValue())).
                filter(orderMaster -> {
                    Date endTime = orderMaster.getFlastreceivedate() == null ? new Date() : orderMaster.getFlastreceivedate();
                    int gapDays = (int) (Math.abs(endTime.getTime() - System.currentTimeMillis()) / 1000 / 60 / 60 / 24 + 1);
                    return gapDays >= limitDays;
                }).map(OrderMasterSearchDTO::getId).collect(toSet());
    }

    /**
     * @param limitDays
     * @return java.util.List<java.lang.Integer>
     * @description: 获取验收超时单id
     * @date: 2020/12/2 16:16
     * @author: zengyanru
     */
    private Set<Integer> getAcceptTimeoutOrderIdList(List<OrderMasterSearchDTO> masterSearchList, Integer limitDays) {
        List<Integer> statusList = New.list(OrderStatusEnum.WaitingForReceive.value);
        // 正常订单且状态为待验收
        return masterSearchList.stream().
                filter(s -> statusList.contains(s.getStatus())).
                filter(s -> s.getSpecies().equals(ProcessSpeciesEnum.NORMAL.getValue())).
                filter(masterSearch -> {
                    Date deliveryTime = masterSearch.getFdeliverydate() == null ? new Date() : masterSearch.getFdeliverydate();
                    int gapDays = (int) (Math.abs(System.currentTimeMillis() - deliveryTime.getTime()) / 1000 / 60 / 60 / 24 + 1);
                    // 超时记录
                    return gapDays >= limitDays;
                }).map(OrderMasterSearchDTO::getId).collect(toSet());
    }

    /**
     * @param orgId
     * @param businessCodeEnum
     * @param codeBaseConfigMap
     * @return java.lang.Integer
     * @description: 根据配置的枚举 获取超时时间
     * @date: 2020/12/3 10:14
     * @author: zengyanru
     */
    @ServiceLog(description = "根据配置的枚举 获取超时时间", serviceType = ServiceType.COMMON_SERVICE)
    private Integer configTimeoutDays(Integer orgId, TimeOutConfigType businessCodeEnum, Map<String, List<BaseConfigDTO>> codeBaseConfigMap, TimeOutBusinessType timeOutBusinessType) {
        // 获取此单位配置-验收限制日期，结算限制日期
        Integer limitDay = 30;

        List<TimeoutStatisticsDO> timeOutStatisticsList = timeoutStatisticsMapper.queryByOrgIdAndDepIdIn(orgId, null);
        if (CollectionUtils.isEmpty(timeOutStatisticsList)) {
            List<BaseConfigDTO> limitDayConfList = codeBaseConfigMap.get(businessCodeEnum.getCode());
            String configValue = limitDayConfList.get(0).getConfigValue();
            try {
                Integer configLimitDays = Integer.valueOf(configValue);
                return CollectionUtils.isEmpty(limitDayConfList) ? businessCodeEnum.getDefaultSet() : configLimitDays;
            } catch (Exception e) {
                throw new BusinessInterceptException(ExecptionMessageEnum.CONFIG_INPUT_ERROR);
            }
        }

        // 有timeout数据时候的时间查找
        Map<TimeOutBusinessType, List<TimeoutStatisticsDO>> collect = timeOutStatisticsList.stream().collect(Collectors.groupingBy(s -> TimeOutBusinessType.getByValue(s.getType()), Collectors.toList()));
        if (!collect.isEmpty()) {
            for (Map.Entry<TimeOutBusinessType, List<TimeoutStatisticsDO>> entry:collect.entrySet()) {
                TimeOutBusinessType c = entry.getKey();
                // 有结算或验收超时的时间配置就查找并重新设值(单次查询仅接受结算或者验收）
                if (timeOutBusinessType.equals(c)) {
                    if (CollectionUtils.isNotEmpty(collect.get(c))) {
                        limitDay = collect.get(c).get(0).getOldConfigDay();
                    }
                    break;
                }
            }
        }
        return limitDay;
    }



    /**
     * @description: 确定合同上传状态
     * @date: 2020/12/3 10:14
     * @author: zengyanru
     */
    private Map<Integer, Integer> orderUploadContractInfo(Map<String, List<BaseConfigDTO>> codeBaseConfigMap, List<OrderMasterSearchDTO> masterSearchList, Integer orgId) {
        // 获取合同配置
        List<BaseConfigDTO> configList = codeBaseConfigMap.get(ConfigCodeEnum.ORDER_CONTRACT_UPLOAD_JSON.name());
        String contractConfigJson =CollectionUtils.isEmpty(configList) ? StringUtils.EMPTY : configList.get(0).getConfigValue();

        List<OrderMasterDO> orderMasterList = OrderMasterTranslator.toOrderMasterDOList(masterSearchList);

        // 收集所有订单详情并按订单ID分组
        List<OrderDetailDO> allDetailList = New.list();
        for (OrderMasterSearchDTO masterSearch : masterSearchList) {
            List<OrderDetailDO> detailList = OrderDetailTranslator.toOrderDetailDOList(masterSearch.getOrderDetail());
            // 设置订单ID
            for (OrderDetailDO detailDO : detailList) {
                detailDO.setFmasterid(masterSearch.getId());
            }
            allDetailList.addAll(detailList);
        }
        Map<Integer, List<OrderDetailDO>> orderDetailMap = OrderDetailTranslator.groupDetailsByOrderId(allDetailList);

        return orderContractService.getOrderContractUploadStatus(orderMasterList, orderDetailMap, contractConfigJson, orgId);
    }

    /**
     * @param codeBaseConfigMap
     * @return java.lang.Boolean
     * @description: 判断是否需要图片验收
     * @date: 2020/12/3 10:15
     * @author: zengyanru
     */
    private Boolean getPhotoAcceptance(Map<String, List<BaseConfigDTO>> codeBaseConfigMap) {
        List<BaseConfigDTO> baseConfigList = codeBaseConfigMap.get(ConfigConstant.ORG_RECEIPT_PIC_CONFIG);
        List<String> configValueList = baseConfigList.stream().map(BaseConfigDTO::getConfigValue).collect(toList());
        if (CollectionUtils.isEmpty(configValueList)) {
            return false;
        }
        int configValue = Integer.parseInt(configValueList.get(0));
        return Objects.equals(configValue,1);
    }

    /**
     * @param orderDetails
     * @return java.lang.Boolean
     * @description: 根据订单退货状态设置订单超时状态, 退货中则不纳入超时
     * @date: 2020/12/3 10:15
     * @author: zengyanru
     */
    private Boolean verificationOrderIfTimeOut(List<OrderDetailVO> orderDetails) {
        for (OrderDetailVO orderDetail : orderDetails) {
            Integer returnStatus = orderDetail.getReturnStatus();
            // 退货中的商品不计入超时
            if (OrderDetailReturnStatus.WAITINGFORCONFIRM.getCode().equals(returnStatus) ||
                    OrderDetailReturnStatus.AGREETORETURN.getCode().equals(returnStatus) ||
                    OrderDetailReturnStatus.REFUSEDTORETURN.getCode().equals(returnStatus) ||
                    OrderDetailReturnStatus.RETURNEDGOODS.getCode().equals(returnStatus)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param detailSearchList
     * @return java.util.List<com.ruijing.store.order.base.core.vo.myorderlist.OrderDetailVO>
     * @description: 构造detail信息，折扣或者优惠价等
     * @date: 2020/12/3 10:16
     * @author: zengyanru
     */
    @ServiceLog(description = "订单详情搜索与展示结果信息转换", serviceType = ServiceType.COMMON_SERVICE)
    private List<OrderDetailVO> getOrderDetailsByOrder(List<OrderDetailSearchDTO> detailSearchList, Integer orderStatus,
                                                       Boolean useStatementCheck, boolean isHms, Integer orgId, List<OrderDetailExtraDTO> orderDetailExtraList, Integer statementId, List<OrderDetailDO> orderDetailDOList) {


        List<OrderDetailVO> orderDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(detailSearchList)) {
            List<Integer> statusCanReturnGoods = New.list(OrderStatusEnum.WaitingForReceive.value,
                    OrderStatusEnum.WaitingForStatement_1.value,
                    OrderStatusEnum.OrderReceiveApproval.value,
                    OrderStatusEnum.OrderReceiveApprovalTwo.value,
                    OrderStatusEnum.ORDER_RECEIVE_APPROVAL_LEVEL_TWO_REJECT.value,
                    OrderStatusEnum.PLATFORM_OPERATOR_APPROVAL.getValue(),
                    OrderStatusEnum.PLATFORM_OPERATOR_APPROVAL_REJECT.getValue()
                    );
            //1.oms实时配置不用结算系统
            //2.oms使用了结算系统 但订单不走结算
            if (Boolean.FALSE.equals(useStatementCheck)) {
                // 20210412，配置可选是否使用锐竞结算，若不使用锐竞结算系统，已完成的订单在15天内也可以发起退货，在发起退货按钮再检验天数
                statusCanReturnGoods.add(OrderStatusEnum.Finish.value);
            }else if (OrderStatusEnum.Finish.getValue().equals(orderStatus) && Objects.isNull(statementId)){
                statusCanReturnGoods.add(OrderStatusEnum.Finish.value);
            }
            boolean canReturnGoods = statusCanReturnGoods.contains(orderStatus);
            Map<Integer, List<OrderDetailExtraDTO>> detailId2ExtraMap = DictionaryUtils.groupBy(orderDetailExtraList, OrderDetailExtraDTO::getOrderDetailId);
            Map<Integer, OrderDetailDO> detailIdDetailMap = CollectionUtils.isEmpty(orderDetailDOList) ? New.emptyMap() : DictionaryUtils.toMap(orderDetailDOList, OrderDetailDO::getId, Function.identity());
            for (OrderDetailSearchDTO detailSearch : detailSearchList) {
                OrderDetailVO orderDetail = this.detailSearchToVOTransform(detailSearch);
                // 价格
                if (detailSearch.getModifyPrice().equals(0)) {
                    if (orderDetail.getActivityOff() > 0) {
                        orderDetail.setPrice(detailSearch.getFbidprice());
                        orderDetail.setTotalPrice(detailSearch.getFbidamount());
                        orderDetail.setShowOriginalPrice(true);
                    }
                } else {
                    orderDetail.setShowOriginalPrice(true);
                }
                orderDetail.setOriginAmount(detailSearch.getOriginalAmount());

                // 商品分类,cas号
                orderDetail.setFirstCategoryId(detailSearch.getFirstCategoryId());
                orderDetail.setFirstCategoryTag(detailSearch.getFirstCategoryName());
                orderDetail.setSecondCategoryId(detailSearch.getSecondCategoryId());
                orderDetail.setSecondCategoryTag(detailSearch.getSecondCategoryName());
                Integer thirdCategoryId = Objects.equals(detailSearch.getSecondCategoryId(), detailSearch.getCategoryId()) || Objects.equals(detailSearch.getFirstCategoryId(), detailSearch.getCategoryId()) ? null : detailSearch.getCategoryId();
                orderDetail.setThirdCategoryId(thirdCategoryId);
                String thirdCategoryName = Objects.equals(detailSearch.getSecondCategoryName(), detailSearch.getCategoryName()) || Objects.equals(detailSearch.getFirstCategoryName(), detailSearch.getCategoryName())? null : detailSearch.getCategoryName();
                orderDetail.setThirdCategoryTag(thirdCategoryName);
                orderDetail.setCategoryId(detailSearch.getCategoryId());
                orderDetail.setCasNo(detailSearch.getCasNo());

                // 危化品处理
                if (detailSearch.getDangerousType() == null) {
                    orderDetail.setDangerousTag(DangerousTypeEnum.UN_DANGEROUS.getName());
                } else {
                    orderDetail.setDangerousTag(DangerousTypeEnum.get(detailSearch.getDangerousType()).getName());
                }
                // 管制品类型
                orderDetail.setRegulatoryType(detailSearch.getRegulatoryType());
                orderDetail.setRegulatoryTypeName(detailSearch.getRegulatoryTypeName());

                // 可退货数目统计
                Double quantityCanReturn;
                if (canReturnGoods) {
                    Double returnQuantity = detailSearch.getFcancelquantity() == null ? 0.00 : detailSearch.getFcancelquantity();
                    Integer quantity = detailSearch.getFquantity() == null ? 0 : detailSearch.getFquantity();
                    quantityCanReturn = BigDecimal.valueOf(quantity)
                            .subtract(BigDecimal.valueOf(returnQuantity))
                            .doubleValue();
                } else {
                    quantityCanReturn = 0.00;
                }
                orderDetail.setQuantityCanReturn(quantityCanReturn);

                // 商品扩展信息
                List<OrderDetailExtraDTO> orderDetailExtraDTOS = detailId2ExtraMap.get(orderDetail.getId());
                setOrderDetailVOExtraInfo(orderDetail, orderDetailExtraDTOS);

                OrderDetailDO dbDetail = detailIdDetailMap.get(orderDetail.getId());
                if(dbDetail != null && dbDetail.getNegotiatedPrice() != null){
                    orderDetail.getShowTagList().add(OrderListDetailTagEnum.COMMODITY_AGREEMENTS_GOODS.getTagName());
                }
                orderDetailList.add(orderDetail);
            }
            // 采购规则校验
            if(!isHms) {
                PurchaseRuleRequestDTO purchaseRuleRequestDTO = new PurchaseRuleRequestDTO();
                purchaseRuleRequestDTO.setOrgIdList(New.list(orgId));
                purchaseRuleRequestDTO.setCheckNodeTypeEnum(CheckNodeTypeEnum.SAVE_CART);
                List<RuleProductVerificationDTO> verificationDTOList = New.list();
                for (int i = 0; i < detailSearchList.size(); i++) {
                    OrderDetailSearchDTO detailSearch = detailSearchList.get(i);
                    RuleProductVerificationDTO verificationDTO = new RuleProductVerificationDTO();
                    verificationDTO.setSeqId((long) i);
                    verificationDTO.setProductId(detailSearch.getProductId());
                    verificationDTO.setProductName(detailSearch.getFgoodname());
                    verificationDTO.setCategoryId(detailSearch.getCategoryId());
                    verificationDTO.setBrandId(detailSearch.getFbrandid());
                    verificationDTO.setSuppId(detailSearch.getSuppId());
                    verificationDTO.setPrice(BigDecimal.valueOf(detailSearch.getOriginalPrice()));
                    verificationDTOList.add(verificationDTO);
                }
                purchaseRuleRequestDTO.setRuleProductVerificationList(verificationDTOList);
                List<PurchaseRuleResultDTO>  purchaseRuleResultDTOS = storeRuleClient.purchaseRulesVerify(purchaseRuleRequestDTO);
                for (PurchaseRuleResultDTO purchaseRuleResultDTO : purchaseRuleResultDTOS) {
                    OrderDetailVO orderDetail = orderDetailList.get(purchaseRuleResultDTO.getSeqId().intValue());
                    orderDetail.setRuleRemindMsg(purchaseRuleResultDTO.getErrorMessage());
                    orderDetail.setPurchaseRuleType(purchaseRuleResultDTO.getPurchaseRuleType());
                }
            }
        }
        return orderDetailList;
    }

    /**
     * 填充订单详情扩展字段
     *
     * @param orderDetailExtraDTOS 订单详情扩展信息
     * @param orderDetail          订单详情VO
     */
    private void setOrderDetailVOExtraInfo(OrderDetailVO orderDetail, List<OrderDetailExtraDTO> orderDetailExtraDTOS) {
        OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
        orderDetailDTO.setFspec(orderDetail.getSpecification());
        orderDetailDTO.setFirstCategoryId(orderDetail.getFirstCategoryId());
        this.setOrderDetailDTOExtraInfo(orderDetailDTO, orderDetailExtraDTOS);
        OrderDetailTranslator.fillSpecificationDetails(orderDetail, orderDetailDTO);
    }

    public void setOrderDetailDTOExtraInfo(OrderDetailDTO orderDetailDTO, List<OrderDetailExtraDTO> orderDetailExtraDTOS){
        if (CollectionUtils.isEmpty(orderDetailExtraDTOS)) {
            return;
        }
        Map<Integer, OrderDetailExtraDTO> type2DetailExtraMap = getType2DetailExtraMap(orderDetailExtraDTOS);
        // 包装规格 , 化学试剂和危险化学品 取值详情的specification，其他分类取PACKING_UNIT
        if (New.set(CategoryConstant.DANGEROUS_ID, CategoryConstant.CHEMICAL_REAGENTS_ID).contains(orderDetailDTO.getFirstCategoryId())) {
            orderDetailDTO.setPackingSpec(orderDetailDTO.getFspec());
        } else {
            OrderDetailExtraDTO packUnitExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.PACKING_UNIT.getType());
            if (Objects.nonNull(packUnitExtraDTO)) {
                orderDetailDTO.setPackingSpec(packUnitExtraDTO.getExtraValue());
            }
        }
        // 浓度
        OrderDetailExtraDTO concentrationExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.CONCENTRATION.getType());
        if (Objects.nonNull(concentrationExtraDTO)) {
            orderDetailDTO.setPurity(concentrationExtraDTO.getExtraValue());
        }

        // 注册编码
        OrderDetailExtraDTO registCertExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.MEDICAL_DEVICE_REGIS_CERT_NUMBER.getType());
        if (Objects.nonNull(registCertExtraDTO)) {
            orderDetailDTO.setMedicalDeviceRegisCertNumber(registCertExtraDTO.getExtraValue());
        }

        // 完成周期
        OrderDetailExtraDTO completionCycleExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.COMPLETION_CYCLE.getType());
        if (Objects.nonNull(completionCycleExtraDTO)) {
            orderDetailDTO.setCompletionCycle(completionCycleExtraDTO.getExtraValue());
        }

        // 出版社
        OrderDetailExtraDTO pressExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.PRESS.getType());
        if (Objects.nonNull(pressExtraDTO)) {
            orderDetailDTO.setPress(pressExtraDTO.getExtraValue());
        }
        // 产品规格
        OrderDetailExtraDTO productSpecExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.PRODUCT_SPECIFICATION.getType());
        if (Objects.nonNull(productSpecExtraDTO)) {
            orderDetailDTO.setProductSpec(productSpecExtraDTO.getExtraValue());
        }
        // 型号
        OrderDetailExtraDTO modelExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.MODEL_NUMBER.getType());
        if (Objects.nonNull(modelExtraDTO)) {
            orderDetailDTO.setModelNumber(modelExtraDTO.getExtraValue());
        }
    }

    /**
     * @param orderTypeEnum
     * @param totalPrice
     * @param orderDetailList
     * @return java.lang.String
     * @description: 计算原总价
     * @date: 2020/12/3 10:16
     * @author: zengyanru
     */
    private String calculateOriginalTotalPrice(Integer orderTypeEnum, BigDecimal totalPrice, List<OrderDetailVO> orderDetailList) {
        if (OrderTypeEnum.BID_ORDER.getCode().equals(orderTypeEnum)) {
            // 竞价单
            return totalPrice.toString();
        } else {
            // 采购单
            BigDecimal originalTotalPrice = BigDecimal.ZERO;
            for (OrderDetailVO orderDetail : orderDetailList) {
                if (orderDetail.isShowOriginalPrice()) {
                    BigDecimal curOriginAmount = new BigDecimal(orderDetail.getOriginAmount().toString());
                    originalTotalPrice = originalTotalPrice.add(curOriginAmount);
                } else {
                    BigDecimal curTotalPrice = new BigDecimal(orderDetail.getTotalPrice().toString());
                    originalTotalPrice = originalTotalPrice.add(curTotalPrice);
                }
            }
            return String.valueOf(originalTotalPrice);
        }
    }

    /**
     * @param detailSearch
     * @return com.ruijing.store.order.base.core.vo.myorderlist.OrderDetailVO
     * @description: orderDetail相关信息转换
     * @date: 2020/12/4 16:33
     * @author: zengyanru
     */
    @ServiceLog(description = "转换订单详情搜索信息到返回体",serviceType = ServiceType.COMMON_SERVICE)
    public OrderDetailVO detailSearchToVOTransform(OrderDetailSearchDTO detailSearch) {
        // 商品原价
        BigDecimal originalPrice = BigDecimal.valueOf(detailSearch.getOriginalPrice());

        OrderDetailVO orderDetailModel = new OrderDetailVO();
        orderDetailModel.setId(detailSearch.getDetailId());
        orderDetailModel.setGoodsName(detailSearch.getFgoodname());
        orderDetailModel.setSpecification(detailSearch.getFspec());
        orderDetailModel.setBrand(detailSearch.getFbrand());
        orderDetailModel.setUnit(detailSearch.getFunit());
        orderDetailModel.setQuantity(detailSearch.getFquantity().doubleValue());

        orderDetailModel.setPrice(detailSearch.getFbidprice());
        if (detailSearch.getFquantity() != null && originalPrice != null) {
            orderDetailModel.setTotalPrice(detailSearch.getFbidamount());
            // 避免double类型计算的精度丢失
            Preconditions.isTrue(detailSearch.getOriginalAmount()!=null,"商品原价不可为空，搜索信息未同步数据库信息,订单详情id为"+detailSearch.getDetailId());
            BigDecimal activityOff = new BigDecimal(Double.toString(detailSearch.getOriginalAmount())).subtract(new BigDecimal(Double.toString(detailSearch.getFbidamount())));
            Double activityOffFixPrecision = Double.valueOf(activityOff.toString());
            orderDetailModel.setActivityOff(activityOffFixPrecision);
        }

        orderDetailModel.setModifyPriceCheck(detailSearch.getModifyPrice()!=null && detailSearch.getModifyPrice().equals(1));
        orderDetailModel.setPicturePath(detailSearch.getPicPath());
        orderDetailModel.setGoodsCode(detailSearch.getFgoodcode());
        orderDetailModel.setReturnStatus(detailSearch.getReturnStatus());
        orderDetailModel.setProductSn(detailSearch.getProductId());
        orderDetailModel.setOriginalPrice(originalPrice != null ? originalPrice.doubleValue() : 0.0);
        orderDetailModel.setShowOriginalPrice(false);
        orderDetailModel.setCategoryTag(detailSearch.getCategoryTag());

        return orderDetailModel;
    }

    /**
     * @param masterSearch
     * @return java.lang.Boolean
     * @description: 判断是否需要入库
     * @date: 2020/12/4 16:33
     * @author: zengyanru
     */
    private Boolean checkNeedInBound(OrderMasterSearchDTO masterSearch) {
        return InventoryStatusEnum.WAITING_FOR_STORE.getCode().equals(masterSearch.getInventoryStatus()) &&
                (masterSearch.getStatus().equals(OrderStatusEnum.WaitingForStatement_1.value) ||
                        masterSearch.getStatus().equals(OrderStatusEnum.Statementing_1.value) ||
                        masterSearch.getStatus().equals(OrderStatusEnum.Finish.value) ||
                        masterSearch.getStatus().equals(OrderStatusEnum.Assess.value));
    }

    /**
     * @param masterSearch
     * @param permitDeptIdList
     * @return java.lang.Boolean
     * @description: 查找配置判断是否允许入库
     * @date: 2020/12/4 16:34
     * @author: zengyanru
     */
    private Boolean canInbound(OrderMasterSearchDTO masterSearch, List<Integer> permitDeptIdList) {
        // 普通配置的出入库
        Boolean flagCanInbound = Boolean.FALSE;
        // 一层if，区分是否需要判断出入库按钮显示
        if (Boolean.TRUE.equals(this.checkNeedInBound(masterSearch))) {
            Integer deptId = masterSearch.getFbuydepartmentid();
            // 二层查库(不需要循环查库，已在外围查找)，此处用有权限的dept判断即可
            if (permitDeptIdList != null && permitDeptIdList.contains(deptId)) {
                flagCanInbound = true;
            }
        }
        return flagCanInbound;
    }

    /**
     * @param masterSearch
     * @param permitDeptIdList
     * @param useNewWarehouseConfig
     * @return java.lang.Integer
     * @description: 获取提交入库许可值，0不可以，1可以
     * @date: 2020/12/4 16:34
     * @author: zengyanru
     */
    private Integer getSubmitPermissionValue(OrderMasterSearchDTO masterSearch, List<Integer> permitDeptIdList, Boolean useNewWarehouseConfig) {
        // 不是指定单位不可以用新库房系统
        if (Boolean.FALSE.equals(useNewWarehouseConfig)) {
            return 0;
        }

        // 是否配置权限
        if (permitDeptIdList != null && permitDeptIdList.contains(masterSearch.getFbuydepartmentid())) {
            return judgeOrderCanSubmitWarehouse(masterSearch);
        }
        return 0;
    }

    /**
     * 获取是否订单满足入库条件
     * @param masterSearch 订单
     * @return 0不可以，1可以
     */
    private int judgeOrderCanSubmitWarehouse(OrderMasterSearchDTO masterSearch){
        //判断订单状态
        if (OrderStatusEnum.WaitingForStatement_1.getValue().equals(masterSearch.getStatus())
                && InventoryStatusEnum.WAITING_FOR_STORE.getCode().equals(masterSearch.getInventoryStatus())
                && !WAIT_STATE_INBOUND_NOT_VALID_ORG.contains(masterSearch.getFuserid())) {
            // 撤销入库特殊需求——待结算+待入库，可以显示入库按钮
            return 1;
        } else if ((OrderStatusEnum.Statementing_1.getValue().equals(masterSearch.getStatus()) || OrderStatusEnum.Finish.getValue().equals(masterSearch.getStatus()))
                && InventoryStatusEnum.WAITING_FOR_STORE.getCode().equals(masterSearch.getInventoryStatus())
                && !STATE_INBOUND_NOT_VALID_ORG.contains(masterSearch.getFuserid())) {
            // 撤销入库特殊需求——结算中或已完成+待入库，可以显示入库按钮
            return 1;
        }
        return 0;
    }

    /**
     * @param codeBaseConfigMap
     * @return boolean
     * @description: 是否采用新库房系统
     * @date: 2020/12/4 16:34
     * @author: zengyanru
     */
    public boolean isUseNewWarehouseSystem(Map<String, List<BaseConfigDTO>> codeBaseConfigMap) {
        List<BaseConfigDTO> systemConfigDTOList = codeBaseConfigMap.get(ConfigConstant.USE_WAREHOUSE_SYSTEM);
        List<BaseConfigDTO> versionConfigDTOList = codeBaseConfigMap.get(ConfigConstant.WAREHOUSE_SYSTEM_VERSION_CODE);
        List<BaseConfigDTO> baseConfigDTOList = systemConfigDTOList;
        baseConfigDTOList.addAll(versionConfigDTOList);

        if (CollectionUtils.isEmpty(baseConfigDTOList)) {
            return false;
        }
        String useWarehouseSystem = null;
        String warehouseSystemVersion = null;
        for (BaseConfigDTO baseConfigDTO : baseConfigDTOList) {
            if (ConfigConstant.USE_WAREHOUSE_SYSTEM.equals(baseConfigDTO.getConfigCode())) {
                useWarehouseSystem = baseConfigDTO.getConfigValue();
            } else if (ConfigConstant.WAREHOUSE_SYSTEM_VERSION_CODE.equals(baseConfigDTO.getConfigCode())) {
                warehouseSystemVersion = baseConfigDTO.getConfigValue();
            }
        }
        Boolean isUseWarehouseSystem = StringUtils.isNotBlank(useWarehouseSystem) && useWarehouseSystem.equals(ConfigConstant.USE_WAREHOUSE_SYSTEM_VALUE);
        Boolean isWarehouseSystemVersion = StringUtils.isNotBlank(warehouseSystemVersion) && warehouseSystemVersion.equals(ConfigConstant.WAREHOUSE_SYSTEM_VERSION_VALUE);

        return isUseWarehouseSystem && isWarehouseSystemVersion;
    }

    /**
     * @param masterSearch
     * @return java.lang.Integer
     * @description: 是否可以打印出入库单
     * @date: 2020/12/4 16:35
     * @author: zengyanru
     */
    private Integer canPrintInAndOutWarehouse(OrderMasterSearchDTO masterSearch) {
        // 控制不需要显示打印出入库单的单位
        List<Integer> hidePrintOrgIdList = New.list(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN.getValue()
                , OrgEnum.GUANG_ZHOU_HU_XI_JIAN_KANG_YAN_JIU_YUAN.getValue()
                , OrgEnum.LING_NAN_NONG_KE_SHI_YAN_SHI.getValue()
                , OrgEnum.WU_YI_DA_XUE.getValue()
                , OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_BA_YI_YUAN.getValue()
                , OrgEnum.JIANG_MEN_SHI_FU_YOU_BAO_JIAN_YUAN.getValue()
                , OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_LIU_YI_YUAN.getValue()
                , OrgEnum.XIANG_GANG_DA_XUE_SHEN_ZHEN_YI_YUAN.getValue()
                , OrgEnum.GUANG_ZHOU_HONG_SHI_ZI_HUI_YI_YUAN.getValue());
        if (hidePrintOrgIdList.contains(masterSearch.getFuserid())) {
            return 0;
        }

        if (InventoryStatusEnum.COMPLETE.getCode().equals(masterSearch.getInventoryStatus())) {
            return 1;
        }
        // 江苏省人民医院的线下采购订单打印验收单, 无需入库也要能打印入库单, 他们是用入库单按钮控制打印验收单...无语
        if (Objects.equals(OrgEnum.JIANG_SU_SHENG_REN_MIN_YI_YUAN.getValue(),masterSearch.getFuserid())
                && OrderSpeciesEnum.OFFLINE.getValue().equals(masterSearch.getSpecies())
                && InventoryStatusEnum.NOT_INBOUND.getCode().equals(masterSearch.getInventoryStatus())
        ) {
            return 1;
        }
        return 0;
    }

    /**
     * @param status
     * @return java.util.List<java.lang.Integer>
     * @description: 获取是否模糊搜索
     * @date: 2020/12/4 16:31
     * @author: zengyanru
     */
    private List<Integer> getsearchStatus(Integer status) {
        List<Integer> searchStatusList = new ArrayList<>();
        if (status == null) {
            return searchStatusList;
        }
        if (status.equals(OrderStatusEnum.Statementing_1.getValue())) {
            searchStatusList.add(OrderStatusEnum.Statementing_1.getValue());
        } else if (status.equals(OrderStatusEnum.WaitingForStatement_1.getValue())) {
            searchStatusList.add(OrderStatusEnum.WaitingForStatement_1.getValue());
        } else if (status.equals(OrderStatusEnum.Finish.getValue())) {
            searchStatusList.add(OrderStatusEnum.Finish.getValue());
            searchStatusList.add(OrderStatusEnum.Assess.getValue());
        } else {
            searchStatusList.add(status);
        }
        return searchStatusList;
    }

    /**
     * @param masterSearch
     * @return com.ruijing.store.order.base.core.vo.myorderlist.OrderMasterVO
     * @description: 将订单主表数据转化为OrderModel返回
     * @date: 2020/11/25 18:25
     * @author: zengyanru
     */
    public OrderMasterVO masterSearchToVo(OrderMasterSearchDTO masterSearch) {
        OrderMasterVO order = new OrderMasterVO();

        // 各种信息
        Integer orderType = masterSearch.getOrderType();
        order.setOrderType(orderType);
        order.setId(masterSearch.getId());
        if (Objects.nonNull(masterSearch.getFbuyerid())) {
            order.setBuyUserId(masterSearch.getFbuyerid());
        }
        order.setBuyerName(masterSearch.getFbuyername());
        order.setOrgName(masterSearch.getFusername());
        order.setOrderNo(masterSearch.getForderno());
        // 退货特殊处理
        BigDecimal returnAmount = masterSearch.getReturnAmount() == null ? BigDecimal.ZERO : BigDecimal.valueOf(masterSearch.getReturnAmount());
        order.setTotalPrice(BigDecimal.valueOf(masterSearch.getForderamounttotal()));
        order.setTotalPriceAfterReturn(BigDecimal.valueOf(masterSearch.getForderamounttotal()).subtract(returnAmount));
        order.setSuccessfulReturnAmount(returnAmount);
        order.setSupplierName(masterSearch.getFsuppname());
        order.setOrderDate(masterSearch.getForderdate());
        order.setDepartmentName(masterSearch.getFbuydepartment());
        order.setBuyerTelephone(masterSearch.getFbuyertelephone());
        // 代配送地址的特殊处理，不显示转送前的地址
        String receiverAddress = masterSearch.getFbiderdeliveryplace();
        if (receiverAddress != null && receiverAddress.contains(" 转送 ")) {
            OrderAddressDTO addressDTO = orderAddressRPCClient.findByOrderId(masterSearch.getId());
            if (addressDTO != null) {
                receiverAddress = StringUtils.defaultIfBlank(addressDTO.getProvince(), StringUtils.EMPTY)
                        + StringUtils.defaultIfBlank(addressDTO.getCity(), StringUtils.EMPTY)
                        + StringUtils.defaultIfBlank(addressDTO.getRegion(), StringUtils.EMPTY)
                        + StringUtils.defaultIfBlank(addressDTO.getAddress(), StringUtils.EMPTY);
            }
        }
        order.setFbiderdeliveryplace(receiverAddress);
        order.setBuyerContactMan(masterSearch.getFbuyercontactman());
        order.setSupplierId(masterSearch.getFsuppid());
        order.setOrgId(masterSearch.getFuserid());
        order.setStatus(masterSearch.getStatus());
        order.setFundStatus(masterSearch.getFundStatus());
        order.setCheckMan(masterSearch.getFlastreceiveman());
        order.setInventoryStatus(masterSearch.getInventoryStatus());
        order.setRelateInfo(masterSearch.getRelateInfo());
        order.setBuyAppId(masterSearch.getFtbuyappid());
        order.setBidOrderId(masterSearch.getBidOrderId());
        order.setCancelDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, masterSearch.getFcanceldate()));
        if (masterSearch.getFlastreceivedate() != null) {
            order.setLastReceiveDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, masterSearch.getFlastreceivedate()));
        }
        if (masterSearch.getFdeliverydate() != null) {
            order.setDeliveryDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT,masterSearch.getFdeliverydate()));
        }
        if (masterSearch.getFconfirmdate() != null) {
            order.setConfirmDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, masterSearch.getFconfirmdate()));
        }

        BigDecimal carryFee = masterSearch.getCarryFee() == null ? BigDecimal.ZERO : BigDecimal.valueOf(masterSearch.getCarryFee());
        order.setTotalCarryFee(carryFee);

        order.setBuyDepartmentId(masterSearch.getFbuydepartmentid());
        order.setCollegeName(masterSearch.getDepartmentParentName());
        order.setSpecies(masterSearch.getSpecies());
        // 是否完成后上传文件（尝试市一的需求）
        List<OrderExtraDTO> orderExtraList = masterSearch.getOrderExtra();
        for (OrderExtraDTO extraDTO : orderExtraList) {
            if (OrderExtraEnum.UPLOAD_SERVICE_TECHNOLOGY_CONTRACT_STATUS.getValue().equals(extraDTO.getExtraKey())) {
                order.setUploadedAfterComplete(true);
                break;
            }
        }
        order.setStatementId(masterSearch.getStatementId());
        return order;
    }

    /**
     * @param masterSearch
     * @param orgCode
     * @param statementIdMap
     * @return com.ruijing.store.order.base.core.bo.myorderlist.OrderStatementBriefInfoBO
     * @description: 获取结算单概览信息
     * @date: 2020/11/25 18:24
     * @author: zengyanru
     */
    private OrderStatementBriefInfoBO getStatementBriefInfo(OrderMasterSearchDTO masterSearch, String orgCode, Map<Long, StatementResultDTO> statementIdMap) {

        OrderStatementBriefInfoBO statementBriefInfo = new OrderStatementBriefInfoBO();
        // 结算单查询
        if (masterSearch.getStatementId() != null && StringUtils.isNoneBlank(orgCode)) {
            StatementResultDTO statement = statementIdMap.get(masterSearch.getStatementId().longValue());

            if (statement != null) {
                if (statement.getStatus() != null) {
                    // 结算单状态
                    statementBriefInfo.setStatus(statement.getStatus());
                }

                if (statement.getBalanceDate() != null) {
                    statementBriefInfo.setBalanceDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, statement.getBalanceDate()));
                }

                if (statement.getEndDate() != null) {
                    statementBriefInfo.setEndDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, statement.getEndDate()));
                }
            }
            return statementBriefInfo;
        } else {
            return null;
        }
    }

    /**
     * @description: 分页搜索查询的专用，如果是聚合查询则只采用没有page的方法
     * @date: 2021/1/13 18:03
     * @author: zengyanru
     * @param searchRequest
     * @param request
     * @return void
     */
    @Override
    public void constructSearchPageParam(OrderSearchParamDTO searchRequest, OrderListRequest request) {
        // 设置页码，本页条目数，其他的基础信息，不一定是通过搜索进来的
        if (request.getPageNo() == null || request.getPageSize() == null) {
            searchRequest.setStartHit(0);
        } else {
            searchRequest.setStartHit((request.getPageNo() - 1) * request.getPageSize());
        }
        searchRequest.setPageSize(request.getPageSize());
        this.constructSearchParam(searchRequest, request);
    }

    /**
     * @param searchRequest
     * @param request
     * @return com.ruijing.store.order.api.search.dto.OrderSearchParamDTO
     * @description: 搜索条件构建
     * @date: 2020/11/25 18:23
     * @author: zengyanru
     */
    private void constructSearchParam(OrderSearchParamDTO searchRequest, OrderListRequest request) {
        // 未传递采购人id，则不设置检索
        if (Objects.nonNull(request.getFbuyerId())) {
            searchRequest.setBuyerIdList(New.list(request.getFbuyerId()));
        }
        // 代配送信息
        searchRequest.setDeliveryProxyOn(request.getDeliveryProxyOn());
        if (CollectionUtils.isNotEmpty(request.getDeliveryType())) {
            searchRequest.setDeliveryTypeList(request.getDeliveryType());
        }
        searchRequest.setDeliveryStatus(request.getDeliveryStatus());
        searchRequest.setDeliveryUser(request.getDeliveryUser());
        searchRequest.setSortedUser(request.getSortedUser());
        searchRequest.setWxDeliveryProxyKeyword(request.getWxDeliveryProxyKeyword());
        searchRequest.setDeliveredTimeStart(request.getDeliveredTimeStart());
        searchRequest.setDeliveredTimeEnd(request.getDeliveredTimeEnd());

        // 设置单位
        if (request.getOrgId() != null) {
            searchRequest.setOrgIdList(New.list(request.getOrgId()));
        }
        searchRequest.setOrgCode(request.getOrgCode());
        searchRequest.setOrgName(request.getOrgName());

        // 搜索订单状态，某些订单状态的特殊处理
        List<Integer> searchStatus = this.getsearchStatus(request.getStatus());
        if (CollectionUtils.isNotEmpty(request.getStatusList())) {
            searchStatus.addAll(request.getStatusList());
        }
        request.setStatusList(searchStatus);
        searchRequest.setStatusList(searchStatus);

        // 需要去除的状态
        if (CollectionUtils.isNotEmpty(request.getExcludeStatusList())) {
            searchRequest.addExcludeStatusList(request.getExcludeStatusList());
        }

        // 精确筛选的经费卡院区
        if (StringUtils.isNotBlank(request.getCampusCode())) {
            searchRequest.setCampusCode(request.getCampusCode());
        }
        if (StringUtils.isNotBlank(request.getCampusName())) {
            searchRequest.setCampusName(request.getCampusName());
        }


        // 模糊搜：采购人、供应商
        String paramSearch = request.getSearch();
        if (StringUtils.isNotBlank(paramSearch)) {
            searchRequest.setSearchKey(paramSearch);
            searchRequest.addFullTextMasterFields("fbuyername");
            searchRequest.addFullTextMasterFields("fsuppname");
            searchRequest.addFullTextMasterFields("forderno");
            searchRequest.addFullTextMasterFields("fbuydepartment");
            searchRequest.addFullTextDetailFields("fgoodname");
            searchRequest.addFullTextDetailFields("fgoodcode");
        }

        String wxSearch = request.getWxSearch();
        if (StringUtils.isNotBlank(wxSearch)) {
            searchRequest.setSearchKey(wxSearch);
            searchRequest.addFullTextMasterFields("fbuyername");
            searchRequest.addFullTextMasterFields("fsuppname");
            searchRequest.addFullTextMasterFields("forderno");
            searchRequest.addFullTextDetailFields("fgoodname");
            searchRequest.addFullTextDetailFields("fgoodcode");
        }

        //供应商名称
        String suppName = request.getSuppname();
        if (StringUtils.isNotBlank(suppName)) {
            searchRequest.setSuppName(suppName);
        }

        // 线上、线下单, 中大 中大深圳不显示线下单 9-表示全部单（历史遗留，不知道枚举在哪里）
        Integer species = request.getProcessSpecies();
        if (species != null && 9 != species) {
            searchRequest.setSpecies(ProcessSpeciesEnum.getByValue(species));
        }
        if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(request.getOrgId())) {
            if (ProcessSpeciesEnum.OFFLINE.getValue().equals(species)) {
                BusinessErrUtil.isTrue(false, ExecptionMessageEnum.OFFLINE_ORDER_MANAGEMENT_SYSU);
            }
            searchRequest.setSpecies(ProcessSpeciesEnum.NORMAL);
        }

        // 订单授权审批IDs todo:中大相关的配置
        List<Integer> cardAuthOrderIdList = request.getCardAuthOrderIds();
        if (cardAuthOrderIdList != null) {
            if (CollectionUtils.isNotEmpty(cardAuthOrderIdList)) {
                // TODO:中大相关方法，初步验证无用到
            } else {
                searchRequest.setBuyerIdList(New.list(request.getFbuyerId()));
            }
        }

        // 采购人名称
        String buyerName = request.getFbuyername();
        if (StringUtils.isNotBlank(buyerName)) {
            searchRequest.setBuyerName(buyerName);
        }

        // 商品名称、货号，单独或多字段搜索，设置了精确的值会覆盖模糊多字段的值
        List<String> fuzzyGoodsInfo = New.list("fgoodname", "fgoodcode","cas_no");
        if (StringUtils.isNotBlank(request.getProductName())) {
            fuzzyGoodsInfo.remove("fgoodname");
            searchRequest.setGoodsName(request.getProductName());
        }
        if (StringUtils.isNotBlank(request.getProductCode())) {
            fuzzyGoodsInfo.remove("fgoodcode");
            searchRequest.setGoodsCode(request.getProductCode());
        }
        if (StringUtils.isNotBlank(request.getCasNo())) {
            fuzzyGoodsInfo.remove("cas_no");
            searchRequest.setCasNo(request.getCasNo());
        }
        // 分类搜索
        if (CollectionUtils.isNotEmpty(request.getFirstCategoryIdList())) {
            searchRequest.setFirstCategoryIdList(request.getFirstCategoryIdList());
        }
        if (CollectionUtils.isNotEmpty(request.getSecondCategoryIdList())) {
            searchRequest.setSecondCategoryIdList(request.getSecondCategoryIdList());
        }
        if (CollectionUtils.isNotEmpty(request.getThirdCategoryIdList())) {
            searchRequest.setThirdCategoryIdList(request.getThirdCategoryIdList());
        }
        // 经费卡搜索
        if (request.getCardNo() != null) {
            searchRequest.setCardNoList(New.list(request.getCardNo()));
        }
        if (CollectionUtils.isNotEmpty(request.getCardIdList())) {
            searchRequest.setCardIdList(request.getCardIdList());
        }

        String productNameOrCode = request.getProductNameOrCode();
        if (StringUtils.isNotBlank(productNameOrCode) && CollectionUtils.isNotEmpty(fuzzyGoodsInfo)) {
            for (String field : fuzzyGoodsInfo) {
                searchRequest.addFullTextDetailFields(field);
            }
            searchRequest.setDetailSearchKey(productNameOrCode);
        }

        // 分类标签搜索
        if (request.getCategoryTag() != null) {
            searchRequest.setCategoryTag(request.getCategoryTag());
        }

        // cas 号，完全匹配搜索
        if (StringUtils.isNotBlank(request.getCasNo())) {
            searchRequest.setCasNo(request.getCasNo());
        }
        searchRequest.setBrandName(request.getBrand());

        // 订单号
        String orderNo = request.getOrderNo();
        if (StringUtils.isNotBlank(orderNo)) {
            orderNo = orderNo.toUpperCase();
            searchRequest.setOrderNo(orderNo);
        }

        // 订单号列表
        List<String> orderNoList = request.getOrderNoList();
        if (CollectionUtils.isNotEmpty(orderNoList)) {
            searchRequest.setOrderNoList(orderNoList.stream().map(String::toUpperCase).collect(toList()));
        }

        // 订单id(主要给到订单详情用)
        Integer orderId = request.getOrderId();
        if (Objects.nonNull(orderId)) {
            searchRequest.setOrderId(orderId);
        }

        // 课题组
        searchRequest.setDepartmentIdList(request.getDeptIdList());
        searchRequest.setDepartmentName(request.getDepartmentName());

        // 开始时间-结束时间 todo fe 传带hms的字串，argue
        String startDate = "1970-01-01 00:00:00";
        String endDate = "2100-01-01 00:00:00";
        if (StringUtils.isNotBlank(request.getStartDate())) {
            if (request.getStartDate().contains("-")) {
                // yyyy-MM-dd 格式
                startDate = request.getStartDate() + " 00:00:00";
                Date startDateCheck = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, startDate);
                BusinessErrUtil.isTrue(startDateCheck != null, ExecptionMessageEnum.START_TIME_FORMAT_ERROR);
            } else {
                // 时间戳格式
                Long startTimestamp = Long.valueOf(request.getStartDate());
                startDate = DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, new Date(startTimestamp));
            }
        }
        if (StringUtils.isNotBlank(request.getEndDate())) {
            if (request.getEndDate().contains("-")) {
                // yyyy-MM-dd 格式
                endDate = request.getEndDate() + " 23:59:59";
                Date endDateCheck = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, endDate);
                BusinessErrUtil.isTrue(endDateCheck != null, ExecptionMessageEnum.END_TIME_FORMAT_ERROR);
            } else {
                // 时间戳格式
                Long endTimestamp = Long.valueOf(request.getEndDate());
                endDate = DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, new Date(endTimestamp));
            }
        }
        FieldRangeDTO orderDateRange = new FieldRangeDTO("forderdate", startDate, endDate);
        searchRequest.setFieldRangeList(New.list(orderDateRange));

        searchRequest.setOrderDateSort(SortOrderEnum.DESC);
        SortOrderEnum orderDateSort = SortOrderEnum.getEnumByCode(request.getOrderDateSort());
        if (Objects.nonNull(orderDateSort)) {
            searchRequest.setOrderDateSort(orderDateSort);
        }

        // 出入库状态
        Integer inventoryStatus = request.getInventoryStatus();
        if (OrderInventoryStatusEnum.UNDONE.getCode().equals(inventoryStatus)) {
            searchRequest.setInventoryStatusList(New.list(InventoryStatusEnum.WAITING_FOR_STORE.getCode()));
        } else if (OrderInventoryStatusEnum.DONE.getCode().equals(inventoryStatus)) {
            searchRequest.setInventoryStatusList(New.list(
                    InventoryStatusEnum.WAITING_FOR_REVIEW.getCode(),
                    InventoryStatusEnum.FAILED.getCode(),
                    InventoryStatusEnum.COMPLETE.getCode()));
        } else if (OrderInventoryStatusEnum.NOT.getCode().equals(inventoryStatus)) {
            searchRequest.setInventoryStatusList(New.list(InventoryStatusEnum.NOT_INBOUND.getCode()));
        } else if (inventoryStatus != null) {
            searchRequest.setInventoryStatusList(New.list(inventoryStatus));
        }

        // 经费状态, 暨大，上九，医学所等的个性化
        if (CollectionUtils.isNotEmpty(request.getFundStatus())) {
            List<Integer> fundStatus = request.getFundStatus();
            searchRequest.setFundStatusList(fundStatus);
        }

        if(StringUtils.isNotBlank(request.getReceiveMan())){
            searchRequest.setReceiveMan(request.getReceiveMan());
        }

        if(StringUtils.isNotBlank(request.getBuyerContactMan())){
            searchRequest.setBuyerContactMan(request.getBuyerContactMan());
        }
        if(StringUtils.isNotBlank(request.getProductSearchContent())){
            searchRequest.setProductSearchContent(request.getProductSearchContent());
        }
        searchRequest.setDockingStatus(request.getDockingStatusList());
        searchRequest.setRelateInfo(request.getRelateInfo());
    }

    /**
     * @description: 订单查看 部门单查看逻辑, 构造进入搜索的部门id列表
     * @date: 2021/2/25 17:19
     * @author: zengyanru
     * @param deptList
     * @param request
     * @param rootDepartmentId
     * @return java.util.List<java.lang.Integer>
     */
    // TODO: 是否进入部门逻辑，由deptlist控制（如果是个人，deptlist传空，如果课题组订单，deptlist不传空）
    @Override
    public List<Integer> getDeptIdListForSearch(List<DepartmentDTO> deptList, OrderListRequest request, Integer rootDepartmentId) {
        List<Integer> deptIdList = deptList.stream().map(DepartmentDTO::getId).collect(toList());
        if (request.getDepartment() != null) {
            return New.list(request.getDepartment());
        } else if (CollectionUtils.isNotEmpty(request.getDepartmentIds())) {
            // 为什么会有 Department 和 DepartmentIds? 因为最初设计没考虑多个课题组的情况, Department先继续保留
            return request.getDepartmentIds();
        }
        else if (!deptIdList.contains(rootDepartmentId)) {
            // 管理部门（学院）且不是根部门者，增加展示该学院全部课题组（父子关系）
            List<Integer> collegeIdList = Optional.ofNullable(deptList).orElse(New.list())
                    .stream().filter(d -> DepartmentTypeEnum.ADMINISTRATING.getValue() == d.getDepartmentType() && !rootDepartmentId.equals(d.getId()))
                    .map(DepartmentDTO::getId)
                    .collect(toList());
            if (CollectionUtils.isNotEmpty(collegeIdList)) {
                List<DepartmentDTO> allChildDepts = departmentRpcClient.batchGetChildDeptList(request.getOrgId(), collegeIdList);
                if (CollectionUtils.isNotEmpty(allChildDepts)) {
                    List<Integer> childDeptIdList = allChildDepts.stream().map(DepartmentDTO::getId).collect(toList());
                    deptIdList.addAll(childDeptIdList);
                }
            }
            return deptIdList.stream().distinct().collect(toList());
        } else {
            return null;
        }
    }

    /**
     * @param rjSessionInfo
     * @param limitDaysRequest
     * @return java.lang.Boolean
     * @description: 判断订单某个状态后经过的天数是否在传入天数内，目前仅用于不使用结算单位
     * @date: 2021/4/12 16:25
     * @author: zengyanru
     */
    @Override
    // TODO:先给接口定义前端，方便构造页面
    public Boolean limitDaysAfterFinish(RjSessionInfo rjSessionInfo, OrderStatusLimitDaysRequest limitDaysRequest) {
        Preconditions.isTrue(rjSessionInfo != null && rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER) != null, "获取登录信息为空，请重新登录");
        Preconditions.isTrue(limitDaysRequest != null && limitDaysRequest.getOrderId() != null, "判断订单某个状态后经过的天数是否在传入天数内方法入参为空");
        Integer orderStatus = limitDaysRequest.getStatus() == null ? OrderStatusEnum.Finish.getValue() : limitDaysRequest.getStatus();

        OrderMasterDO orderMaster = orderMasterMapper.selectByPrimaryKey(limitDaysRequest.getOrderId());
        BusinessErrUtil.isTrue(orderStatus.equals(orderMaster.getStatus()), ExecptionMessageEnum.CHECK_ORDER_STATUS_DAYS_PASSED, OrderStatusEnum.get(orderMaster.getStatus()).getName(), OrderStatusEnum.get(orderStatus).getName());

        //最新验收时间
        Date flastreceivedate = orderMaster.getFlastreceivedate();
        long timeGap = System.currentTimeMillis() - flastreceivedate.getTime();
        return timeGap < (limitSeconds * 1000);
    }

    // 入参校验
    private void orderListReqControll(OrderListRequest req) {
        String wxSearch = req.getWxSearch();
        String suppname = req.getSuppname();
        String startDate = req.getStartDate();
        String search = req.getSearch();
        String productNameOrCode = req.getProductNameOrCode();
        String orderNo = req.getOrderNo();
        String fbuyername = req.getFbuyername();
        String endDate = req.getEndDate();
        String inventoryStatus = req.getInventoryStatus() == null ? null : req.getInventoryStatus().toString();
        String campusCode = req.getCampusCode();

        // 字符串入参校验
        String errorMsg = "输入搜索字符不可超过100个字符";
        Integer limitLength = 100;
        List<String> checkStringList = New.list(wxSearch,suppname,startDate,search,productNameOrCode,orderNo,
                fbuyername,endDate,inventoryStatus, campusCode);
        this.checkInputLength(checkStringList,limitLength,errorMsg);
    }

    // 入参String长度限制,TODO:后续可以开发为通用方法
    private void checkInputLength(List<String> textList,Integer limitLength, String errorMsg){
        for (String text : textList) {
            Boolean flag = text == null || text.length() <= limitLength;
            Preconditions.isTrue(flag,errorMsg);
        }
    }


    @Override
    public Boolean cancelOrder(RjSessionInfo rjSessionInfo, ApplyCancelOrderReqDTO cancelOrderReq) {
        // 控制入参(TODO:如果方法太多则需要抽提为一个方法)
        Preconditions.notNull(cancelOrderReq, "取消订单入参不能为空");
        Preconditions.notNull(cancelOrderReq.getOrderId(), "取消订单入参id不能为空");
        // 控制订单的采购人和当前登录用户是同一个人--from 余琪
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        // 取消订单
        cancelOrderReq.setFcancelman(loginInfo.getUserName());
        cancelOrderReq.setFcancelmanid(loginInfo.getUserId().toString());
        cancelOrderReq.setStatus(OrderStatusEnum.PurchaseApplyToCancel.getValue());
        cancelOrderReq.setOrgId(loginInfo.getOrgId());
        cancelOrderManageService.cancelOrder(cancelOrderReq);
        return true;
    }

    @Override
    public ReceiptOrderResponseDO acceptOrder(RjSessionInfo rjSessionInfo, OrderReceiptParamDTO receiptOrderReq) {
        // 控制入参
        BusinessErrUtil.notNull(receiptOrderReq, "验收订单入参不能为空");
        Integer orderId = receiptOrderReq.getOrderId();
        BusinessErrUtil.notNull(orderId, "验收订单入参id不能为空");
        // 补充需要的入参信息
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), false, null);
        receiptOrderReq.setOrgId(loginInfo.getOrgId());
        receiptOrderReq.setOrgCode(loginInfo.getOrgCode());
        receiptOrderReq.setUserId(loginInfo.getUserId());
        receiptOrderReq.setUserName(loginInfo.getUserName());
        receiptOrderReq.setDepartmentIdList(loginInfo.getDeptIdList());
        receiptOrderReq.setUserGuid(loginInfo.getUserGuid());

        // 管理平台配置了收货的，不允许在我们平台进行验收
        OrgDockingConfigDTO orgDockingConfigDTO = dockingConfigCommonService.getConfig(loginInfo.getOrgCode());
        if(orgDockingConfigDTO.getEnable()){
            OrderMasterDO checkParam = new OrderMasterDO();
            checkParam.setId(receiptOrderReq.getOrderId());
            boolean configEnable = dockingConfigCommonService.isNewDockingEnable(orgDockingConfigDTO, checkParam, null);
            boolean acceptInExternal = OmsDockingConfigValueEnum.ACCEPT_IN_EXTERNAL.name().equals(orgDockingConfigDTO.getOrderDockingConfigDTO().getOrderSyncStatusAccept());
            String hint = orgDockingConfigDTO.getOrderDockingConfigDTO().getOrderDisableReceiveButtonHint();
            hint = StringUtils.isEmpty(hint) ? LocaleUtils.translate(ExecptionMessageEnum.MOVE_TO_MANAGEMENT_PLATFORM_FOR_ACCEPTANCE) : hint;
            boolean allowAccept = !(configEnable && acceptInExternal);
            if (offlineNoPushOrgSet.contains(loginInfo.getOrgId())) {
                // 线下单不推送的单位，线下单允许在采购平台验收
                OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(receiptOrderReq.getOrderId());
                boolean isOldOrder = oldDateService.isOldDate(orderMasterDO.getFuserid(), orderMasterDO.getForderdate(), orderMasterDO.getFundStatus(), Integer.valueOf(orderMasterDO.getSpecies()));
                allowAccept = allowAccept || (isOldOrder || ProcessSpeciesEnum.OFFLINE.getValue().equals(orderMasterDO.getSpecies().intValue()));
            }
            BusinessErrUtil.isTrue(allowAccept, hint);
        }
        List<OrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderId), New.list(OrderExtraEnum.ACCEPTANCE_WAY.getValue()));
        if(CollectionUtils.isNotEmpty(orderExtraDTOList)){
            BusinessErrUtil.isTrue(!OrderAcceptanceWayEnum.SCAN_BARCODE.value.toString().equals(orderExtraDTOList.get(0).getExtraValue()), ExecptionMessageEnum.QR_CODE_ACCEPTANCE_REQUIRED);
        }

        return orderAcceptService.userAcceptOrder(receiptOrderReq);
    }

    /**
     * 批量验收订单
     *
     * @param batchAcceptRequest
     * @return 成功与否
     */
    @Override
    public Boolean batchAcceptOrder(OrderBasicParamDTO batchAcceptRequest, LoginUserInfoBO loginInfo) {
        Preconditions.notNull(batchAcceptRequest, "批量验收订单入参不可为null");
        Preconditions.notEmpty(batchAcceptRequest.getOrderIdList(),"批量验收订单入参订单id列表不可为null");
        // TODO: 如果太慢则走搜索
        List<OrderMasterDO> orderMasterList = orderMasterMapper.findByIdIn(batchAcceptRequest.getOrderIdList());

        // 获取配置
        Map<String, String> configMap = sysConfigClient.getConfigMapByOrgCodeAndConfigCode(loginInfo.getOrgCode(),
                New.list(ConfigConstant.CONFIG_CODE_PROCUREMENT_ACCEPTANCE,
                        ConfigConstant.ORG_RECEIPT_PIC_CONFIG,
                        ConfigCodeEnum.ORDER_CONTRACT_UPLOAD_JSON.name()));
        BusinessErrUtil.notNull(configMap, ExecptionMessageEnum.UNIT_CONFIG_UNAVAILABLE);

        // 验收方式
        Boolean crossAccept = configMap.get(ConfigConstant.CONFIG_CODE_PROCUREMENT_ACCEPTANCE)
                .equals(OrderAcceptWayEnum.CROSS_ACCEPTANCE.value.toString());
        Boolean picAccept = configMap.get(ConfigConstant.ORG_RECEIPT_PIC_CONFIG)
                .equals(ConfigConstant.NEED_PIC_VALUE.toString());

        // 退货单判断
        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderIds(batchAcceptRequest.getOrderIdList());
        List<GoodsReturn> goodsInReturnStatusList = New.list();
        List<Integer> returningStatusList = New.list(
                GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode(),
                GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode(),
                GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode(),
                GoodsReturnStatusEnum.RETURNED_GOODS.getCode());
        if (CollectionUtils.isNotEmpty(goodsReturnList)) {
            goodsInReturnStatusList = goodsReturnList.stream().filter(s -> returningStatusList.contains(s.getGoodsReturnStatus())).collect(toList());
            BusinessErrUtil.isTrue(CollectionUtils.isEmpty(goodsInReturnStatusList), ExecptionMessageEnum.NON_CANCELLED_RETURN_ORDER_EXISTS);
        }

        // 合同配置获取
        String orderContractConfigJson = configMap.get(ConfigCodeEnum.ORDER_CONTRACT_UPLOAD_JSON.name());
        OrderContractConfig contractConfig = null;
        if (StringUtils.isNotBlank(orderContractConfigJson)) {
            contractConfig = JsonUtils.fromJson(orderContractConfigJson, OrderContractConfig.class);
        }

        List<OrderContract> contractList = orderContractMapper.selectByOrderIdIn(batchAcceptRequest.getOrderIdList());
        Map<Integer, List<OrderContract>> orderIdContractList = contractList.stream().collect(groupingBy(OrderContract::getOrderId));

        // 批量验收前为了避免部分成功部分不成功的情况，先做一次可否验收的校验
        for (OrderMasterDO orderMaster : orderMasterList) {
            // 验收方式，交叉验收还是自行验收（看单的采购人）
            if (Boolean.TRUE.equals(crossAccept)) {
                BusinessErrUtil.isTrue(!orderMaster.getFbuyerid().equals(loginInfo.getUserId()), ExecptionMessageEnum.UNIT_CROSS_ACCEPTANCE_NOT_ALLOWED);
            }
            // 拍照验收的话不能批量验收
            BusinessErrUtil.isTrue(!picAccept, ExecptionMessageEnum.BATCH_ACCEPTANCE_NOT_ALLOWED);
            // 所有的单是否在能够验收的状态
            BusinessErrUtil.isTrue(orderMaster.getStatus().equals(OrderStatusEnum.WaitingForReceive.value), ExecptionMessageEnum.ORDER_NOT_PENDING_ACCEPTANCE);

            // 合同校验-按金额限制上传
            if (Objects.nonNull(contractConfig)
                    && Objects.equals(contractConfig.getUploadContract(), OrderUploadContractEnum.YES.getValue())
                    && Objects.equals(contractConfig.getUploadCondition(), OrderContractUploadConditionEnum.AMOUNT_LIMIT_UPLOAD.getValue())) {
                // 是否超出金额
                double orderAmountLimit = NumberUtils.toDouble(contractConfig.getAmountLimit());
                if (orderAmountLimit > 0) {
                    BigDecimal orderAmount = orderMaster.getForderamounttotal().subtract(BigDecimal.valueOf(orderMaster.getReturnAmount()));
                    boolean overLimit = orderAmount.compareTo(BigDecimal.valueOf(orderAmountLimit)) >= 0;
                    if (overLimit) {
                        // 超出金额，应当上传了合同
                        List<OrderContract> curContractList = orderIdContractList.get(orderMaster.getId());
                        BusinessErrUtil.notEmpty(curContractList, ExecptionMessageEnum.ORDER_EXCEEDS_LIMIT_UPLOAD_CONTRACT, orderAmountLimit);
                    }
                }
            }
        }

        // 通过以上判断后，循环验收订单
        for (Integer orderId : batchAcceptRequest.getOrderIdList()) {
            OrderReceiptParamDTO receiptOrderReq = new OrderReceiptParamDTO();
            receiptOrderReq.setOrgId(loginInfo.getOrgId());
            receiptOrderReq.setOrgCode(loginInfo.getOrgCode());
            receiptOrderReq.setUserId(loginInfo.getUserId());
            receiptOrderReq.setUserName(loginInfo.getUserName());
            receiptOrderReq.setOrderId(orderId);
            // 是否在cache redis中（正在进行其他操作）
            orderAcceptService.userAcceptOrder(receiptOrderReq);
        }
        return true;
    }

    /**
     * @param cancelOrderReq
     * @return
     * @description: 拒绝取消订单
     */
    @Override
    public Boolean refuseCancelOrder(RjSessionInfo rjSessionInfo, CancelOrderReqDTO cancelOrderReq) {
        Preconditions.notNull(cancelOrderReq, "拒绝取消订单入口不可为空");
        Preconditions.notNull(cancelOrderReq.getRefuseReason(), "拒绝取消原因不能为空");
        Preconditions.notNull(cancelOrderReq.getOrderMasterId(), "拒绝取消入参订单主表id不可为空");

        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        cancelOrderReq.setCancelManId(loginInfo.getUserId().toString());

        cancelOrderManageService.refuseCancelOrder(cancelOrderReq);
        return true;
    }

    /**
     * @param cancelOrderReqDTO
     * @return 成功与否
     * @description: 同意取消订单
     */
    @Override
    public Boolean agreeCancelOrder(RjSessionInfo rjSessionInfo, CancelOrderReqDTO cancelOrderReqDTO) {
        Preconditions.notNull(cancelOrderReqDTO, "同意供应商取消订单接口入参不可为空");
        Preconditions.notNull(cancelOrderReqDTO.getOrderMasterId(),"同意供应商取消订单接口入参订单id不可为空");
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        cancelOrderReqDTO.setCancelMan(loginInfo.getUserName());
        cancelOrderReqDTO.setCancelManId(loginInfo.getUserId().toString());
        cancelOrderReqDTO.setJobNumber(loginInfo.getJobNumber());
        cancelOrderReqDTO.setOrgCode(loginInfo.getOrgCode());
        cancelOrderReqDTO.setOrgId(rjSessionInfo.getOrgId());
        OrderMasterDO orderMasterInfo = orderMasterMapper.selectByPrimaryKey(cancelOrderReqDTO.getOrderMasterId());
        if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgCode(loginInfo.getOrgCode())) {
            Preconditions.notNull(orderMasterInfo,"查询不到订单信息");
            BusinessErrUtil.isTrue(OrderStatusEnum.SupplierApplyToCancel.value.equals(orderMasterInfo.getStatus()), ExecptionMessageEnum.ORDER_NOT_CANCELLABLE);
            sysuOrderService.agreeCancelOrder(orderMasterInfo, loginInfo);
            return true;
        }
        // 同意取消
        cancelOrderManageService.agreeCancelOrder(cancelOrderReqDTO);
        return true;
    }

    @Override
    public OrderListCountVO countOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) {
        return this.countOrderList(rjSessionInfo, request, false);
    }

    /**
     * @param request
     * @return com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListCountVO
     * @description: 计数不同状态的订单
     * @date: 2021/1/11 10:17
     * @author: zengyanru
     */
    @Override
    public OrderListCountVO countOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request, boolean isHms) {
        // 登录相关信息
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true, ConfigConstant.BUYER_CENTER_ORDER_VIEW);
        List<Integer> deptIdList = loginInfo.getDeptIdList();
        Integer rootDepartmentId = loginInfo.getRootDepartmentId();
        Integer orgId = loginInfo.getOrgId();
        String orgCode = loginInfo.getOrgCode();
        Integer curUserId = loginInfo.getUserId();
        List<DepartmentDTO> deptList = loginInfo.getDeptList();

        // 空部门则不展示
        if (CollectionUtils.isEmpty(deptIdList)) {
            return new OrderListCountVO();
        }
        // 构造搜索的请求体，区分我的订单和课题组订单
        OrderSearchParamDTO param = new OrderSearchParamDTO();
        if(isHms){
            this.handleParamForHms(param, request, loginInfo);
        }
        request.setOrgId(orgId);
        request.setOrgCode(orgCode);
        Map<Integer, Integer> statusCountMap;
        Map<Integer, Integer> deliveryCountMap;
        if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(orgId)) {
            // 中大的特殊订单列表展示逻辑
            sysuOrderService.controllViewOrderAccessSYSU(param, request, rootDepartmentId, loginInfo);
            if (Boolean.TRUE.equals(request.getMyOrderCheck())) {
                param.setBuyerIdList(New.list(curUserId));
            }
            Request searchRequest = orderSearchBoostService.searchRequestTransform(param);
            statusCountMap = orderSearchBoostService.countOrderBySearchRequest(searchRequest);
            // 获取代配送状态map
            searchRequest.getAggsItemList().clear();
            searchRequest.addFilter(new TermFilter("deliveryType",DeliveryTypeEnum.PROXY.getCode()));
            searchRequest.addNotFilter(new TermFilter("status", New.list(OrderStatusEnum.ORDER_SPLIT_UP.getValue())));
            deliveryCountMap = orderSearchBoostService.aggFieldToCountOrderBySearchRequest(searchRequest, "delivery_status");
        } else {
            List<Integer> deptIdListForSearch;
            if (Boolean.TRUE.equals(request.getMyOrderCheck())) {
                deptIdListForSearch = this.getDeptIdListForSearch(New.list(), request, rootDepartmentId);
            } else {
                deptIdListForSearch = this.getDeptIdListForSearch(deptList, request, rootDepartmentId);
            }
            request.setDeptIdList(deptIdListForSearch);
            this.constructSearchParam(param,request);
            param.setOrgIdList(New.list(orgId));

            OrderStatisticsParamDTO orderStatisticsParam = new OrderStatisticsParamDTO();
            orderStatisticsParam.setOrgIds(New.list(orgId));
            orderStatisticsParam.setDeptIds(param.getDepartmentIdList());
            orderStatisticsParam.setFlowIdList(param.getFlowIdList());
            if (Boolean.TRUE.equals(request.getMyOrderCheck())) {
                orderStatisticsParam.setBuyerIds(New.list(curUserId));
                param.setBuyerIdList(New.list(curUserId));
            }
            // 搜索
            statusCountMap = orderSearchBoostService.countOrderByStatus(orderStatisticsParam);
            // 获取代配送状态map
            deliveryCountMap = orderSearchBoostService.countOrderByDeliveryStatus(orderStatisticsParam);
        }
        this.setExcludeStatusStrategy(statusCountMap);

        // 统计结果
        OrderListCountVO orderCount = new OrderListCountVO();
        // 全部
        int allHits = 0;
        for (Map.Entry<Integer, Integer> entry : statusCountMap.entrySet()) {
            allHits += entry.getValue();
        }
        // 待确认
        int waitToConfirm = this.changeNullToZero(statusCountMap.get(OrderStatusEnum.WaitingForConfirm.getValue()));
        // 待发货
        int waitToDelivery = this.changeNullToZero(statusCountMap.get(OrderStatusEnum.WaitingForDelivery.getValue()));
        // 待收货
        int waitToReceive = this.changeNullToZero(statusCountMap.get(OrderStatusEnum.WaitingForReceive.getValue()));
        // 待验收审批(南方医需要特殊逻辑）
        int waitToReceiveApproval = this.changeNullToZero(statusCountMap.get(OrderStatusEnum.OrderReceiveApproval.getValue()));
        if (orgCode.equals(OrgConst.NAN_FANG_YI_KE)) {
            waitToReceiveApproval += this.changeNullToZero(statusCountMap.get(OrderStatusEnum.changeingFundCar.getValue()));
            waitToReceiveApproval += this.changeNullToZero(statusCountMap.get(OrderStatusEnum.changeFundCarFailed.getValue()));
        }
        // 待结算（多种状态合并）
        int waitToSettle = this.changeNullToZero(statusCountMap.get(OrderStatusEnum.WaitingForStatement_1.getValue()));
        // 结算中
        int settling = this.changeNullToZero(statusCountMap.get(OrderStatusEnum.Statementing_1.getValue()));
        // 已完成（多种状态合并）
        int completedOrder = this.changeNullToZero(statusCountMap.get(OrderStatusEnum.Finish.getValue()));
        completedOrder += this.changeNullToZero(statusCountMap.get(OrderStatusEnum.Assess.getValue()));

        // 需要获取经费异常状态的单位。额外通过搜索查记录
        boolean orgOnDemand = orgCode.equals(OrgConst.SHANG_HAI_JIU_YUAN)
                || orgCode.equals(OrgConst.ZHONG_KE_YUAN_ZHONG_JI_SUO)
                || orgCode.equals(OrgConst.JI_NAN_DA_XUE)
                || orgCode.equals(OrgConst.ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN);
        if (orgOnDemand) {
            param.setFundStatusList(New.list(OrderFundStatusEnum.ThrawFailed.getValue()));
            SearchPageResultDTO<OrderMasterSearchDTO> response = orderSearchBoostService.commonSearch(param);
            orderCount.setAbnormalOrderCount(response.getTotalHits().intValue());
        }

        orderCount.setTotal(allHits);
        orderCount.setWaitingForConfirm(waitToConfirm);
        orderCount.setWaitingForDelivery(waitToDelivery);
        orderCount.setWaitingForReceive(waitToReceive);
        orderCount.setOrderReceiveApproval(waitToReceiveApproval);
        orderCount.setWaitingForStatement(waitToSettle);
        orderCount.setSettling(settling);
        orderCount.setCompletedOrderCount(completedOrder);
        // 代配送订单相关
        int deliveryProxyTotal = deliveryCountMap.values().stream().reduce(0, Integer::sum);
        orderCount.setDeliveryProxyCount(deliveryProxyTotal);
        orderCount.setApplyCancelDeliveryProxyCount(this.changeNullToZero(deliveryCountMap.get(DeliveryStatusEnum.SUPP_APPLY_CANCEL.getValue())));
        return orderCount;
    }

    /**
     * @description: 将二元运算简化成单句
     * @date: 2021/1/11 19:29
     * @author: zengyanru
     * @param value
     * @return java.lang.Integer
     */
    private Integer changeNullToZero(Integer value) {
        if (value == null) {
            return 0;
        } else {
            return value;
        }
    }



    /**
     * @param goodsReturnBriefInfoRequest
     * @return java.util.List<com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn.GoodsReturnBriefInfoVO>
     * @description: 查找当前订单的退货单基本信息，用于判断验收和展示退货列表或详情
     * @date: 2021/1/27 14:58
     * @author: zengyanru
     */
    @Override
    public List<GoodsReturnBriefInfoVO> getBriefGoodsReturnInfo(GoodsReturnBriefInfoRequest goodsReturnBriefInfoRequest) {
        Preconditions.notNull(goodsReturnBriefInfoRequest, "查找基本退货信息入参不可为空");
        Preconditions.notNull(goodsReturnBriefInfoRequest.getOrderId(), "查找基本退货信息入参订单id不可为空");
        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderId(goodsReturnBriefInfoRequest.getOrderId());
        goodsReturnList = goodsReturnList.stream().filter(Objects::nonNull).filter(s -> GoodsReturnInvalidEnum.NORMAL.getCode().equals(s.getInvalid())).collect(toList());
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(goodsReturnBriefInfoRequest.getOrderId());
        List<GoodsReturnBriefInfoVO> goodsReturnBriefList = New.list();
        if (CollectionUtils.isEmpty(goodsReturnList)) {
            return goodsReturnBriefList;
        }
        boolean returnAllCheck = Objects.equals(goodsReturnBriefInfoRequest.getReturnAll(), true);
        for (GoodsReturn goodsReturn : goodsReturnList) {
            boolean canContinueAccept = goodsReturn.getGoodsReturnStatus().equals(GoodsReturnStatusEnum.SUCCESS.getCode())
                    || goodsReturn.getGoodsReturnStatus().equals(GoodsReturnStatusEnum.CANCEL_REQUEST.getCode());
            if (canContinueAccept && !returnAllCheck) {
                continue;
            }
            GoodsReturnBriefInfoVO goodsReturnBriefInfo = new GoodsReturnBriefInfoVO();
            goodsReturnBriefInfo.setGoodsReturnStatus(goodsReturn.getGoodsReturnStatus());
            goodsReturnBriefInfo.setOrderId(goodsReturn.getOrderId());
            goodsReturnBriefInfo.setReturnNo(goodsReturn.getReturnNo());
            goodsReturnBriefInfo.setReturnId(goodsReturn.getId());
            goodsReturnBriefInfo.setOrderNo(orderMasterDO.getForderno());
            // 商品名称, 详情id
            List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailList = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturn.getGoodsReturnDetailJSON());
            List<GoodsReturnProductBriefVO> returnBriefInfoList = New.listWithCapacity(goodsReturnInfoDetailList.size());
            for (GoodsReturnInfoDetailVO goodsReturnDetail : goodsReturnInfoDetailList) {
                GoodsReturnProductBriefVO goodsReturnProductBrief = new GoodsReturnProductBriefVO();
                goodsReturnProductBrief.setDetailId(NumberUtils.toInt(goodsReturnDetail.getDetailId()));
                goodsReturnProductBrief.setGoodsName(goodsReturnDetail.getGoodsName());
                returnBriefInfoList.add(goodsReturnProductBrief);
            }
            goodsReturnBriefInfo.setGoodsReturnProductBrief(returnBriefInfoList);
            goodsReturnBriefList.add(goodsReturnBriefInfo);
        }
        return goodsReturnBriefList;
    }


    /**
     * 采购人中心-订单管理待确认订单/待发货增加发货提醒
     * @param rjSessionInfo
     * @param orderBasicParamDTO
     */
    @Override
    public RemoteResponse deliveryRemind(RjSessionInfo rjSessionInfo, OrderBasicParamDTO orderBasicParamDTO) {
        Integer orderId = orderBasicParamDTO.getOrderId();
        Preconditions.notNull(orderId, "订单信息为空!");
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.ORDER_DOES_NOT_EXIST);
        // 判断是否有权限
        BusinessErrUtil.isTrue(("" + orderMasterDO.getFbuyerid()).equals(("" + rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER))), ExecptionMessageEnum.CANNOT_OPERATE_OTHERS_ORDERS);
        Integer status = orderMasterDO.getStatus();
        BusinessErrUtil.isTrue(status.equals(OrderStatusEnum.WaitingForConfirm.getValue()) || status.equals(OrderStatusEnum.WaitingForDelivery.getValue())
                , ExecptionMessageEnum.ONLY_SPECIFIC_STATUS_CAN_RECEIVE_SHIPPING_REMINDERS);

        // 如果不存在则还没有提醒过，则添加为自然天的时间 计算现在距离到明日(00:00:00)还剩下多少时间
        Date nowTime = new Date();
        String todayString = DateUtils.format("yyyy-MM-dd", nowTime);
        String orderDeliveryRemindKey = ORDER_DELIVERY_REMIND_REDIS_KEY + orderBasicParamDTO.getOrderId() + "_time_" +todayString;
        // 如果存在，则说明已经点击过
        if (cacheClient.exists(orderDeliveryRemindKey)) {
            return RemoteResponse.<Boolean>custom().setSuccess().setMsg(LocalI18nUtils.translate("超过当天提醒上限"));
        }

        // 发送邮件/短信通知
        AsyncExecutor.listenableRunAsync(() -> {
            orderMessageHandler.sendDeliveryRemindMessageToSupplier(orderMasterDO);
            // 时间为当前时间到 00:00:00剩余时间，其实过期时间也可以设置一天，但为了节省内存就计算剩余时间吧！
            cacheClient.putToCache(orderDeliveryRemindKey,1,getRemainSecondsOneDay(nowTime));
        }).addFailureCallback(throwable -> {
            logger.error("提醒发货订单通知失败！" + throwable);
            Cat.logError(CAT_TYPE, "orderEmailHandler", "提醒发货订单通知失败！", throwable);
        });

        return RemoteResponse.<Boolean>custom().setSuccess().setMsg(LocalI18nUtils.translate("提醒商家发货成功"));
    }

    @Override
    public RemoteResponse<Boolean> confirmRemind(RjSessionInfo rjSessionInfo, OrderBasicParamDTO orderBasicParamDTO) {
        final String cacheKeyPrefix = "BuyerOrderServiceImpl_confirmRemind_OrderId_";
        Integer orderId = orderBasicParamDTO.getOrderId();
        Preconditions.notNull(orderId, "订单信息为空!");

        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.ORDER_DOES_NOT_EXIST);

        // 权限校验
        BusinessErrUtil.isTrue(("" + orderMasterDO.getFbuyerid()).equals(("" + rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER))),
                ExecptionMessageEnum.CANNOT_OPERATE_OTHERS_ORDERS);

        // 状态校验 - 只能是待确认状态
        BusinessErrUtil.isTrue(orderMasterDO.getStatus().equals(OrderStatusEnum.WaitingForConfirm.getValue()),
                ExecptionMessageEnum.ONLY_WAITING_CONFIRM_STATUS_CAN_RECEIVE_CONFIRM_REMINDERS);

        // Redis防重复提醒
        Date nowTime = new Date();
        String todayString = DateUtils.format("yyyy-MM-dd", nowTime);
        String orderConfirmRemindKey = cacheKeyPrefix + orderBasicParamDTO.getOrderId() + "_time_" + todayString;
        if (cacheClient.exists(orderConfirmRemindKey)) {
            return RemoteResponse.<Boolean>custom().setSuccess().setMsg(LocalI18nUtils.translate("超过当天提醒上限"));
        }

        // 异步发送提醒消息
        AsyncExecutor.listenableRunAsync(() -> {
            orderMessageHandler.sendConfirmRemindMessageToSupplier(orderMasterDO);
            cacheClient.putToCache(orderConfirmRemindKey, 1, getRemainSecondsOneDay(nowTime));
        }).addFailureCallback(throwable -> {
            logger.error("提醒确认订单通知失败！" + throwable);
            Cat.logError(CAT_TYPE, "orderEmailHandler", "提醒确认订单通知失败！", throwable);
        });

        return RemoteResponse.<Boolean>custom().setSuccess().setMsg(LocalI18nUtils.translate("提醒商家确认订单成功"));
    }


    /**
     * 计算距离凌晨 00:00::00的剩余秒数
     * @param currentDate
     * @return
     */
    public static Integer getRemainSecondsOneDay(Date currentDate) {
        LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault());
        long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
        return (int) seconds;
    }


    /**
     *  获取当前采购人及单位 电子签名配置
     * @param rjSessionInfo
     * @return
     */
    @Override
    public ElectronicSignInfoVO searchOperationConfig(RjSessionInfo rjSessionInfo, OrderBasicParamDTO orderBasicParamDTO) {
        Preconditions.notNull(orderBasicParamDTO.getOrderId(),"订单id不能为空！");
        String orgCode = null;
        try {
            orgCode = OrgEnum.getOrgEnumById(rjSessionInfo.getOrgId()).getCode();
        } catch (Exception e) {
            OrganizationDTO organizationDTO = userClient.getOrgById(rjSessionInfo.getOrgId());
            Preconditions.notNull(organizationDTO, "根据orgId获取机构信息为空！");
            orgCode = organizationDTO.getCode();
        }
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderBasicParamDTO.getOrderId());
        ElectronicSignOperationDTO electronicSignOperationDTO = electronicSignServiceClient.searchOperationConfig(rjSessionInfo.getGuid(), orgCode, orderMasterDO.getFbuydepartmentid(), ElectronicSignatureOperationEnum.ORDER_RECEIVE);
        ElectronicSignInfoVO electronicSignInfoVO = ElectronicSignTranslator.electronicSignOperationDTO2ElectronicSignInfoVO(electronicSignOperationDTO);
        return electronicSignInfoVO;
    }

    /**
     * 修改订单地址，记录日志方法。校验与控制
     * @param rjSessionInfo
     * @param modifyAddrRequestDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean modifyOrderAddr(RjSessionInfo rjSessionInfo, ModifyAddrRequestDTO modifyAddrRequestDTO) {
        BusinessErrUtil.isTrue(!ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER)), ExecptionMessageEnum.SYSU_USERS_CANNOT_MODIFY_ADDRESS);
        Preconditions.notNull(modifyAddrRequestDTO.getOrderId(), "请求需要传入订单id");
        List<OrderMasterSearchDTO> masterList = orderSearchBoostService.searchOrderById(modifyAddrRequestDTO.getOrderId());
        BusinessErrUtil.notEmpty(masterList, ExecptionMessageEnum.ORDER_ID_NOT_EXIST_FOR_ADDRESS, modifyAddrRequestDTO.getOrderId());
        OrderMasterSearchDTO masterBeforeModify = masterList.get(0);
        Integer orgId = masterBeforeModify.getFuserid();
        // 判断状态
        BusinessErrUtil.isTrue(OrderStatusEnum.WaitingForConfirm.getValue().equals(masterBeforeModify.getStatus()), ExecptionMessageEnum.MODIFICATION_FAILED_STATUS_CHANGED);
        modifyAddrRequestDTO.setOrderNo(masterBeforeModify.getForderno());

        // 判断是否代配送，代配送不允许修改地址
        OrderAddressDTO address = orderAddressRPCClient.findByOrderId(modifyAddrRequestDTO.getOrderId());
        BusinessErrUtil.isTrue(!DeliveryTypeEnum.PROXY.getCode().equals(address.getDeliveryType()), ExecptionMessageEnum.PROXY_DELIVERY_CANNOT_MODIFY_ADDRESS);

        // 判断前后运费，不相同不允许修改
        Double carryFeeValue = masterBeforeModify.getCarryFee();
        BigDecimal carryFeeBefore = Objects.nonNull(carryFeeValue) ? BigDecimal.valueOf(carryFeeValue).setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
        BigDecimal carryFeeAfter = this.calCarryFeeForOrder(masterBeforeModify, modifyAddrRequestDTO);
        int equalCheck = carryFeeBefore.compareTo(carryFeeAfter);
        BusinessErrUtil.isTrue(equalCheck == 0, ExecptionMessageEnum.MODIFICATION_FAILED_SHIPPING_COST_CHANGED);

        // 判断是否已经修改过地址
        List<OrderApprovalLog> modifyAddrLog = orderApprovalLogMapper.findByOrderIdInAndApproveStatusIn(New.list(modifyAddrRequestDTO.getOrderId()), New.list(OrderApprovalEnum.MODIFIED_DELIVERY_ADDRESS.getValue()));
        BusinessErrUtil.isTrue(CollectionUtils.isEmpty(modifyAddrLog), ExecptionMessageEnum.ADDRESS_MODIFIED_CONTACT_MERCHANT, masterBeforeModify.getForderno());

        // 校验收货地址与地址标签匹配
        if (StringUtils.isNotBlank(modifyAddrRequestDTO.getLabel())) {
            validateAddressLabelMatch(orgId, modifyAddrRequestDTO);
        }

        // 修改状态（原子与并发控制，单个订单的操作）, 先控制此单并发，而后原子操作
        String cacheKey = modifyAddrRequestDTO.getOrderId() + "_modifyOrderAddr";
        try {
            cacheClient.controlRepeatOperation(cacheKey, 3);
            // 监视超时并释放，防止redis重入控制发生跨线程释放锁问题
            CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(
                    () -> modifyDeliveryAddr(rjSessionInfo, modifyAddrRequestDTO), defaultIoExecutor)
                    .exceptionally(throwable -> {
                        Cat.logError(CAT_TYPE, "modifyOrderAddr", throwable.getMessage(), throwable);
                        return null;
                    });
            Boolean successCheck = future.get(2500, TimeUnit.MILLISECONDS);
            if (Boolean.FALSE.equals(successCheck)) {
                throw new BusinessInterceptException(ExecptionMessageEnum.MODIFY_ORDER_DATABASE_ADDRESS_FAILED);
            }
        } catch (Exception e) {
            BusinessErrUtil.isTrue(false, ExecptionMessageEnum.MODIFY_ORDER_ADDRESS_FAILED, modifyAddrRequestDTO.getOrderId() + ",", e.getMessage());
        } finally {
            cacheClient.removeCache(cacheKey);
        }
        // 发邮件和微信
        orderMessageHandler.sendSupplierEmailModifyAddr(masterBeforeModify, modifyAddrRequestDTO);
        return true;
    }

    /**
     * 修改订单地址，记录日志逻辑，写逻辑
     * @param rjSessionInfo
     * @param modifyAddrRequestDTO
     */
    private boolean modifyDeliveryAddr(RjSessionInfo rjSessionInfo,ModifyAddrRequestDTO modifyAddrRequestDTO) {
        try {
            // 修改订单，地址拼装
            StringBuilder newDeliveryPlace = new StringBuilder()
                    .append(modifyAddrRequestDTO.getProvince())
                    .append(modifyAddrRequestDTO.getCity())
                    .append(modifyAddrRequestDTO.getDistrict())
                    .append(modifyAddrRequestDTO.getAddress());

            // 拼接地址标签和地址标签备注
            if (StringUtils.isNotBlank(modifyAddrRequestDTO.getLabel())) {
                // 拼接标签信息
                newDeliveryPlace.append("-").append(modifyAddrRequestDTO.getLabel());
                if (StringUtils.isNotBlank(modifyAddrRequestDTO.getLabelRemarks())) {
                    newDeliveryPlace.append("（").append(modifyAddrRequestDTO.getLabelRemarks()).append("）");
                }
            }


            orderMasterMapper.updateDeliveryPlaceByIdAndControlStatus(newDeliveryPlace.toString(), modifyAddrRequestDTO.getConsignee(),
                    modifyAddrRequestDTO.getMobile(), modifyAddrRequestDTO.getOrderId());
            // 修改地址表
            OrderAddressDTO orderAddressDTO = new OrderAddressDTO();
            orderAddressDTO.setId(modifyAddrRequestDTO.getOrderId());
            orderAddressDTO.setOrderNo(modifyAddrRequestDTO.getOrderNo());
            orderAddressDTO.setReceiverName(modifyAddrRequestDTO.getConsignee());
            orderAddressDTO.setReceiverPhone(modifyAddrRequestDTO.getMobile());
            orderAddressDTO.setProvince(modifyAddrRequestDTO.getProvince());
            orderAddressDTO.setCity(modifyAddrRequestDTO.getCity());
            orderAddressDTO.setRegion(modifyAddrRequestDTO.getDistrict());
            orderAddressDTO.setAddress(modifyAddrRequestDTO.getAddress());
            orderAddressDTO.setLabel(modifyAddrRequestDTO.getLabel());
            orderAddressDTO.setLabelRemarks(modifyAddrRequestDTO.getLabelRemarks());
            orderAddressRPCClient.upsertAddress(orderAddressDTO);

            // 修改日志
            OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
            orderApprovalLog.setOrderId(modifyAddrRequestDTO.getOrderId());
            orderApprovalLog.setReason("修改收货地址");
            orderApprovalLog.setApproveStatus(OrderApprovalEnum.MODIFIED_DELIVERY_ADDRESS.getValue());
            orderApprovalLog.setOperatorId(rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER).intValue());
            orderApprovalLogMapper.insertSelective(orderApprovalLog);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 校验地址标签与收货地址是否匹配
     *
     * @param orgId                组织ID
     * @param modifyAddrRequestDTO 修改地址请求DTO
     */
    private void validateAddressLabelMatch(Integer orgId, ModifyAddrRequestDTO modifyAddrRequestDTO) {
        if (StringUtils.isBlank(modifyAddrRequestDTO.getLabel())) {
            return;
        }

        List<NewDeliveryAddressLabelDTO> addressLabelDTOList = addressClient.queryAddressLabelByOrgIdAndLabelName(orgId, modifyAddrRequestDTO.getLabel());
        if (CollectionUtils.isEmpty(addressLabelDTOList)) {
            return;
        }
        boolean needCheckAddressLabel = addressLabelDTOList.stream()
                .anyMatch(addressLabelDTO -> addressLabelDTO.getRegionType()
                        .equals(RegionTypeEnum.DESIGNATED_REGION.getVal()));
        if (needCheckAddressLabel) {
            NewDeliveryAddressLabelDTO deliveryAddressLabelDTO = addressLabelDTOList.get(0);
            boolean checkResult = StringUtils.equals(modifyAddrRequestDTO.getProvince(), deliveryAddressLabelDTO.getProvince())
                    && StringUtils.equals(modifyAddrRequestDTO.getCity(), deliveryAddressLabelDTO.getCity())
                    && StringUtils.equals(modifyAddrRequestDTO.getDistrict(), deliveryAddressLabelDTO.getDistrict());

            BusinessErrUtil.isTrue(checkResult, ExecptionMessageEnum.ADDRESS_REGION_MISMATCH);
        }
    }


    /**
     *
     * @param orderMaster
     * @param modifyAddrRequestDTO
     * @return
     */
    public BigDecimal calCarryFeeForOrder(OrderMasterSearchDTO orderMaster, ModifyAddrRequestDTO modifyAddrRequestDTO) {
        BizSearchProductCarryFeeDTO carryFeeReq = new BizSearchProductCarryFeeDTO();
        carryFeeReq.setSupplierId(orderMaster.getFsuppid());
        carryFeeReq.setProvince(modifyAddrRequestDTO.getProvince());
        carryFeeReq.setCity(modifyAddrRequestDTO.getCity());

        List<BizSearchProductDetailCarryFeeDTO> carryFeeDetailList = New.list();
        for (OrderDetailSearchDTO orderDetail : orderMaster.getOrderDetail()) {
            BizSearchProductDetailCarryFeeDTO carryFeeDetail = new BizSearchProductDetailCarryFeeDTO();
            carryFeeDetail.setProductId(orderDetail.getProductId());
            carryFeeDetail.setTotalAmount(BigDecimal.valueOf(orderDetail.getFbidamount()).setScale(2, BigDecimal.ROUND_HALF_UP));
            carryFeeDetail.setProductCount(BigDecimal.valueOf(orderDetail.getFquantity()).setScale(0, BigDecimal.ROUND_HALF_UP));
            carryFeeDetailList.add(carryFeeDetail);
        }
        carryFeeReq.setDetailCarryFeeDTOList(carryFeeDetailList);
        List<BizSearchProductDetailCarryFeeDTO> productCarryFeeList = suppClient.listProductCarryFee(carryFeeReq);
        return productCarryFeeList.stream().map(BizSearchProductDetailCarryFeeDTO::getSingleCarryFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 根据订单id查询日志，是否修改过地址
     *
     * @param orderId
     * @return
     */
    @Override
    public OrderAddressInfoVO checkHasModifiedAddr(Integer orderId) {
        Preconditions.notNull(orderId, "查询订单是否修改过地址，需要传入订单id");
        // 判断是否已经修改过地址
        List<OrderApprovalLog> modifyAddrLog = orderApprovalLogMapper.findByOrderIdInAndApproveStatusIn(New.list(orderId), New.list(OrderApprovalEnum.MODIFIED_DELIVERY_ADDRESS.getValue()));
        Boolean hasModifiedAddress = CollectionUtils.isNotEmpty(modifyAddrLog);
        // 判断是否代配送
        OrderAddressDTO address = orderAddressRPCClient.findByOrderId(orderId);
        Boolean proxyCheck = address == null ? false : DeliveryTypeEnum.PROXY.getCode().equals(address.getDeliveryType());

        OrderAddressInfoVO addrInfo = new OrderAddressInfoVO();
        addrInfo.setHasModifiedAddress(hasModifiedAddress);
        addrInfo.setUseProxyDeliver(proxyCheck);
        return addrInfo;
    }


    /**
     * 查询订单列表推送失败项
     *
     * @param rjSessionInfo
     * @param pageQuery
     * @return
     */
    @Override
    public PageableResponse<List<OrderPushFailItemVO>> orderPushFailItemPageQuery(RjSessionInfo rjSessionInfo, OrderPushFailItemDTO pageQuery) {
        OrderSearchParamDTO paramDTO = new OrderSearchParamDTO();
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true, ConfigConstant.BUYER_CENTER_ORDER_VIEW);
        // 登录相关信息
        Integer rootDepartmentId = loginInfo.getRootDepartmentId();
        Integer orgId = loginInfo.getOrgId();
        String orgCode = loginInfo.getOrgCode();
        Integer curUserId = loginInfo.getUserId();
        List<DepartmentDTO> deptList = loginInfo.getDeptList();
        List<Integer> deptIdList = loginInfo.getDeptIdList();

        // 部门列表为空直接返回
        if (CollectionUtils.isEmpty(deptIdList)){
            return PageableResponse.<List<OrderPushFailItemVO>>custom().setData(New.list()).setPageSize(pageQuery.getPageSize()).setPageNo(pageQuery.getPageNo()).setTotal(0).setSuccess();
        }

        OrderListRequest request = new OrderListRequest();
        request.setOrgId(orgId);
        request.setOrgCode(orgCode);
        // 构造搜索用部门列表
        List<Integer> deptIdListForSearch;
        if (pageQuery.getIsMy()){
            deptIdListForSearch = this.getDeptIdListForSearch(New.list(), request, rootDepartmentId);
            paramDTO.setBuyerIdList(New.list(curUserId));
        } else {
            deptIdListForSearch = this.getDeptIdListForSearch(deptList, request, rootDepartmentId);
        }
        paramDTO.setDepartmentIdList(deptIdListForSearch);
        paramDTO.setOrgIdList(New.list(orgId));
        paramDTO.setStartHit((pageQuery.getPageNo() - 1) * pageQuery.getPageSize());
        paramDTO.setPageSize(pageQuery.getPageSize());
        paramDTO.setOrderNo(pageQuery.getOrderNo());
        List<Integer> dockingStatusList = new ArrayList<>();
        dockingStatusList.add(DockingPushStatusEnum.FAILED.getCode());
        dockingStatusList.add(DockingPushStatusEnum.ARGUMENT_EXCEPTION.getCode());
        paramDTO.setDockingStatus(dockingStatusList);

        SearchPageResultDTO<OrderMasterSearchDTO> data = orderSearchBoostService.commonSearch(paramDTO);
        if (data.getTotalHits() == 0 || CollectionUtils.isEmpty(data.getRecordList())){
            return PageableResponse.<List<OrderPushFailItemVO>>custom().setData(New.list()).setPageSize(pageQuery.getPageSize()).setPageNo(pageQuery.getPageNo()).setTotal(0).setSuccess();
        }

        Map<String, OrderMasterSearchDTO> masterSearchDTOMap = data.getRecordList().stream().collect(Collectors.toMap(OrderMasterSearchDTO::getForderno,Function.identity()));
        List<String> orderNoList = data.getRecordList().stream().map(OrderMasterSearchDTO::getForderno).collect(toList());
        // 获取docking_extra信息,创建更新时间（最后一次推送时间）倒序
        List<DockingExtra> dockingExtraList = dockingExtraMapper.findByInfoInAndTypeOrderByUpdateTime(orderNoList, DockingTypeEnum.Order.getValue());
        List<OrderPushFailItemVO> list = dockingExtraList.stream().map(dockingExtra -> {
            OrderMasterSearchDTO orderMasterSearchDTO = masterSearchDTOMap.get(dockingExtra.getInfo());
            OrderPushFailItemVO itemVO = new OrderPushFailItemVO();
            itemVO.setId(orderMasterSearchDTO.getId());
            itemVO.setNumber(orderMasterSearchDTO.getForderno());
            itemVO.setBuyerName(orderMasterSearchDTO.getFbuyername());
            itemVO.setBudgetAmount(BigDecimal.valueOf(orderMasterSearchDTO.getForderamounttotal()));
            itemVO.setPushTime(dockingExtra.getUpdateTime());
            itemVO.setFailReason(dockingExtra.getMemo());
            return itemVO;
        }).collect(toList());
        return PageableResponse.<List<OrderPushFailItemVO>>custom().setSuccess()
                .setTotal(data.getTotalHits())
                .setPageNo(pageQuery.getPageNo())
                .setPageSize(pageQuery.getPageSize())
                .setData(list);
    }


    @Override
    public List<OrderMasterSearchDTO> listStatusChangeNotices(RjSessionInfo rjSessionInfo) {
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        String guid = rjSessionInfo.getGuid();
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(guid, orgId, true, ConfigConstant.ORDER_VIEW);
        List<Integer> deptIdList = loginInfo.getDeptIdList();
        if(CollectionUtils.isEmpty(deptIdList)){
            return New.emptyList();
        }
        Request request = new Request();
        request.setPageSize(3);
        request.setStart(0);
        request.addFilter(new TermFilter("fuserid", orgId));
        request.addFilter(new TermFilter("fbuyerid", rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER)));
        request.addFilter(new TermFilter("fbuydepartmentid", deptIdList));
        request.addFilter(new TermFilter("status",  New.list(OrderStatusEnum.WaitingForReceive.getValue(), OrderStatusEnum.WaitingForStatement_1.getValue(), OrderStatusEnum.SupplierApplyToCancel.getValue())));
        request.addOrderSortItem(new FieldSortItem("update_time", SortOrder.DESC));
        SearchPageResultDTO<OrderMasterSearchDTO> searchDTOSearchPageResultDTO = orderSearchBoostService.search(request);
        return searchDTOSearchPageResultDTO.getRecordList();
    }

    /**
     * 修改订单扩展信息
     *
     */
    @Override
    public void modifyOrderExtInfo(RjSessionInfo rjSessionInfo, ModifyOrderExtInfoRequestDTO request) {
        Preconditions.notNull(request.getOrderId(), "订单ID不能为空");
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(request.getOrderId());
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.ORDER_ID_NOT_FOUND);

        List<BaseOrderExtraDTO> needSaveExtras = New.list();
        // 实验数据网盘链接
        if (StringUtils.isNotBlank(request.getExperimentDataUrl())) {
            BaseOrderExtraDTO exitExtra = OrderExtraTranslator.orderMaterDO2BaseExtra(orderMasterDO, OrderExtraEnum.EXPERIMENT_DATA_URL, request.getExperimentDataUrl());
            needSaveExtras.add(exitExtra);
        }
        if(CollectionUtils.isNotEmpty(needSaveExtras)){
            orderExtraClient.saveList(New.list(needSaveExtras));
        }
    }


    /**
     * 获取课题组id-电子签名配置映射
     *
     * @param masterSearchList ES中的订单列表
     * @param loginInfo        登录人信息
     * @return 课题组id-电子签名配置映射
     */
    private Map<Integer, ElectronicSignConfigVO> getBuyerDepartmentIdESignConfigMap(List<OrderMasterSearchDTO> masterSearchList, LoginUserInfoBO loginInfo) {
        // 获取人在部门的电子签名缓存key
        final String GET_USER_ELECTRONIC_SIGN_IN_DEPARTMENT_CACHE_KEY = "ELECTRONIC_SIGN_";
        // 只有待验收审批的才需要用到，用不到的订单过滤掉
        List<Integer> buyerDepartmentIdList = masterSearchList.stream().filter(item -> OrderStatusEnum.OrderReceiveApproval.getValue().equals(item.getStatus()))
                .map(OrderMasterSearchDTO::getFbuydepartmentid).distinct().collect(Collectors.toList());
        Map<Integer, ElectronicSignConfigVO> buyerDepartmentIdESignConfigMap = New.map();
        for (Integer buyerDepartmentId : buyerDepartmentIdList) {
            String cacheKey = GET_USER_ELECTRONIC_SIGN_IN_DEPARTMENT_CACHE_KEY + loginInfo.getOrgId() + buyerDepartmentId + loginInfo.getUserId();
            ElectronicSignOperationDTO electronicSignOperationDTO = (ElectronicSignOperationDTO) cacheClient.getFromCache(cacheKey);
            if (electronicSignOperationDTO == null) {
                electronicSignOperationDTO = electronicSignServiceClient.searchOperationConfig(loginInfo.getUserGuid(), loginInfo.getOrgCode(), buyerDepartmentId, ElectronicSignatureOperationEnum.ACCEPTANCE_APPROVAL);
                cacheClient.putToCache(cacheKey, electronicSignOperationDTO, 5 * 60);
            }
            buyerDepartmentIdESignConfigMap.put(buyerDepartmentId, ElectronicSignTranslator.dto2Vo(electronicSignOperationDTO));
        }
        return buyerDepartmentIdESignConfigMap;
    }

    /**
     * 构建超时订单查询。对于可能与其他传入的搜索条件互斥的条件，如果互斥则返回false，不进行搜索
     *
     * @param orderSearchParamDTO 订单查询DTO
     * @param request             查询的参数
     * @return 是否构造成功，如果有冲突导致不能查询则返回false
     */
    public boolean constructTimeOutParams(OrderSearchParamDTO orderSearchParamDTO, OrderListRequest request) {
        // 结算超时订单状态
        final List<Integer> balanceTimeOutStatusList = New.list(OrderStatusEnum.WaitingForStatement_1.getValue(), OrderStatusEnum.OrderReceiveApproval.getValue());
        // 验收超时订单状态
        final List<Integer> examineTimeOutStatusList = New.list(OrderStatusEnum.WaitingForReceive.getValue());
        // 需过滤的退货状态
        final List<Integer> notOverTimeReturnStatus = New.list(
                OrderDetailReturnStatus.WAITINGFORCONFIRM.getCode(),
                OrderDetailReturnStatus.AGREETORETURN.getCode(),
                OrderDetailReturnStatus.REFUSEDTORETURN.getCode(),
                OrderDetailReturnStatus.RETURNEDGOODS.getCode());

        // 查询字段
        String queryField;
        // 订单超时状态
        List<Integer> timeOutStatusList;
        // 超时类型
        TimeOutConfigType timeOutConfigType;

        // 目前仅处理结算超时和验收超时
        if (request.getOverTimeType() == null) {
            return true;
        }
        if (TimeOutEnums.BALANCE.getType() == request.getOverTimeType()) {
            // 结算超时： 收货时间 < 当前时间-结算超时配置时间+1
            queryField = "flastreceivedate";
            timeOutConfigType = TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS;
            timeOutStatusList = balanceTimeOutStatusList;
        } else if (TimeOutEnums.EXAMINE.getType() == request.getOverTimeType()) {
            // 验收超时： 发货时间 < 当前时间-验收超时配置时间+1
            queryField = "fdeliverydate";
            timeOutConfigType = TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS;
            timeOutStatusList = examineTimeOutStatusList;
        } else {
            return true;
        }

        // 过滤状态，必须为指定状态的才算超时。如果入参有传状态，则取交集
        List<Integer> statusList = this.retainAllOrCopyList(orderSearchParamDTO.getStatusList(), timeOutStatusList);
        if (CollectionUtils.isEmpty(statusList)) {
            return false;
        }
        orderSearchParamDTO.setStatusList(statusList);

        // 获取超时配置时间
        int timeOutConfigDays = this.getTimeOutConfig(orderSearchParamDTO.getOrgCode(), timeOutConfigType);
        // 计算超时时间，进行范围过滤
        Date timeOutLimitDate = new Date(System.currentTimeMillis() - (long) (timeOutConfigDays - 1) * 24 * 60 * 60 * 1000);
        FieldRangeDTO queryDateRange = new FieldRangeDTO(queryField, null, DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, timeOutLimitDate));
        List<FieldRangeDTO> fieldRangeDTOList = orderSearchParamDTO.getFieldRangeList() == null ? New.list() : orderSearchParamDTO.getFieldRangeList();
        fieldRangeDTOList.add(queryDateRange);
        orderSearchParamDTO.setFieldRangeList(fieldRangeDTOList);

        // 过滤,只能查线上单。如果要查线下单则不能继续查询
        if (request.getProcessSpecies() != null && !ProcessSpeciesEnum.NORMAL.getValue().equals(request.getProcessSpecies())) {
            return false;
        }
        orderSearchParamDTO.setSpecies(ProcessSpeciesEnum.NORMAL);

        // 过滤正在退货处理的订单
        List<Integer> excludeReturnStatusList = this.retainAllOrCopyList(orderSearchParamDTO.getExcludeReturnStatusList(), notOverTimeReturnStatus);
        if (CollectionUtils.isEmpty(excludeReturnStatusList)) {
            return false;
        }
        orderSearchParamDTO.setExcludeReturnStatusList(excludeReturnStatusList);

        if (orderSearchParamDTO.getOrgIdList().contains(OrgEnum.NAN_FANG_YI_KE.getValue())) {
            // 需过滤的经费卡状态
            final List<Integer> notOverTimeFundStatus = New.list(OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue());
            List<Integer> excludeFundStatusList = this.retainAllOrCopyList(orderSearchParamDTO.getExcludeFundStatusList(), notOverTimeFundStatus);
            if (CollectionUtils.isEmpty(excludeFundStatusList)) {
                return false;
            }
            orderSearchParamDTO.setExcludeFundStatusList(excludeFundStatusList);
        }
        return true;
    }

    /**
     * 防止查询条件互斥。如果有集合为空则返回另一个集合的值。
     *
     * @param primaryList   主数组，存于查询条件中的数组
     * @param secondaryList 不变值，用于判断的数组
     * @param <E>           数组泛型
     * @return 数组结果
     */
    private <E> List<E> retainAllOrCopyList(List<E> primaryList, List<E> secondaryList) {
        if (CollectionUtils.isNotEmpty(primaryList)) {
            primaryList.retainAll(secondaryList);
        } else {
            primaryList = New.list(secondaryList);
        }
        return primaryList;
    }

    /**
     * 获取超时订单配置
     *
     * @param orgCode           单位编码
     * @param timeOutConfigType 超时配置类型
     * @return 超时配置天数
     */
    private int getTimeOutConfig(String orgCode, TimeOutConfigType timeOutConfigType) {
        // 1.获取医院机构的配置
        List<BaseConfigDTO> configDTOList = New.emptyList();
        try {
            configDTOList = sysConfigClient.getValueByOrgCodeAndConfigCode(orgCode, New.list(timeOutConfigType.getCode()));
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "getTimeOutConfig", "获取配置异常", e);
        }
        // 查不到或者是非法字符就是取默认时间配置
        return CollectionUtils.isNotEmpty(configDTOList) ? NumberUtils.toInt(configDTOList.get(0).getConfigValue(), timeOutConfigType.getDefaultSet()) : timeOutConfigType.getDefaultSet();
    }

    /**
     * 设置是否需要展示打印按钮
     *
     * @param orderListRespVO        订单数据VO
     * @param baseConfigCodeValueMap 打印按钮显隐配置
     */
    private void setShowPrintButton(OrderListRespVO orderListRespVO, Map<String, List<String>> baseConfigCodeValueMap) {
        List<Integer> printDeliveryNoteStatusList = New.emptyList();
        List<Integer> printAcceptanceStatusList = New.emptyList();
        List<Integer> printWareHouseApplicationStatusList = New.emptyList();
        if (baseConfigCodeValueMap != null) {
            List<String> deliveryNoteConfigList = baseConfigCodeValueMap.getOrDefault(PrintConfigConstant.SHOW_PRINT_DELIVERY_NOTE_BUTTON_STATUS, New.emptyList());
            printDeliveryNoteStatusList = deliveryNoteConfigList.stream().map(item -> item.split(",")).flatMap(Arrays::stream).filter(StringUtils::isNoneBlank).map(Integer::valueOf).collect(toList());

            List<String> acceptanceStatusList = baseConfigCodeValueMap.getOrDefault(PrintConfigConstant.SHOW_PRINT_ACCEPTANCE_BUTTON_STATUS, New.emptyList());
            printAcceptanceStatusList = acceptanceStatusList.stream().map(item -> item.split(",")).flatMap(Arrays::stream).filter(StringUtils::isNoneBlank).map(Integer::valueOf).collect(toList());

            List<String> wareHouseApplicationStatusList = baseConfigCodeValueMap.getOrDefault(PrintConfigConstant.SHOW_PRINT_WAREHOUSE_APPLICATION_BUTTON_STATUS, New.emptyList());
            printWareHouseApplicationStatusList = wareHouseApplicationStatusList.stream().map(item -> item.split(",")).flatMap(Arrays::stream).filter(StringUtils::isNoneBlank).map(Integer::valueOf).collect(toList());
        }
        for (OrderInfoVO orderInfoVO : orderListRespVO.getOrderList()) {
            // 判断HMS单据管理配置与单位个性化的需求，控制送货单显隐
            boolean showPrintDeliveryNote = printDeliveryNoteStatusList.contains(orderInfoVO.getOrder().getStatus());
            orderInfoVO.setShowPrintDeliveryNote(showPrintDeliveryNote && this.getCustomShowPrintDeliveryNote(orderInfoVO));

            boolean showPrintAcceptance = printAcceptanceStatusList.contains(orderInfoVO.getOrder().getStatus());
            orderInfoVO.setShowPrintAcceptance(showPrintAcceptance);

            boolean showPrintWareHouseApplication = printWareHouseApplicationStatusList.contains(orderInfoVO.getOrder().getStatus());
            orderInfoVO.setShowPrintWareHouseApplication(showPrintWareHouseApplication);

            // 重新生成出入库单据按钮 ,目前只有山东中医定制，待结算和结算中展示
            Set<Integer> orderStatusSet = New.set(OrderStatusEnum.WaitingForStatement_1.getValue(), OrderStatusEnum.Statementing_1.getValue());
            boolean showReGenerateWareHouseApplication = orderStatusSet.contains(orderInfoVO.getOrder().getStatus()) && orderInfoVO.getOrder().getOrgId() == OrgEnum.SHAN_DONG_SHENG_ZHONG_YI_YUAN.getValue();
            if (showReGenerateWareHouseApplication) {
                orderInfoVO.getShowButtonList().add(OrderButtonTypeEnum.REGENERATE_WAREHOUSE_APPLICATION);
            }
        }
    }

    /**
     * 获取单位个性化的送货单打印控制逻辑
     *
     * @param orderInfoVO 订单数据
     * @return 是否显示打印按钮
     */
    private boolean getCustomShowPrintDeliveryNote(OrderInfoVO orderInfoVO) {
        OrderMasterVO orderMasterVO = orderInfoVO.getOrder();
        List<OrderDetailVO> orderDetailVOList = orderInfoVO.getOrderDetails();
        if (OrgEnum.LU_JUN_JUN_YI_DA_XUE.getValue() == orderMasterVO.getOrgId()) {
            // 如果是线下单且含药品的商品，不展示[打印送货单]按钮
            final int medicineCategoryId = 2175;
            boolean isOfflineOrder = OrderSpeciesEnum.OFFLINE.getValue().equals(orderMasterVO.getSpecies());
            boolean containsMedicines = orderDetailVOList.stream().anyMatch(detail -> medicineCategoryId == detail.getFirstCategoryId());
            return !(isOfflineOrder && containsMedicines);
        }
        return true;
    }

    /**
     * 单位定制化的旧单标签逻辑
     * @param orgCode 机构单吗
     * @param orderListRespVO 数据
     */
    private void customOrgOldFlag(String orgCode, OrderListRespVO orderListRespVO){
        if(OrgEnum.HUA_NAN_NONG_YE_DA_XUE.getCode().equals(orgCode)){
            Date huaNongOldDate = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, OrderDateConstant.HUA_NAN_NONG_YE_DA_XUE_OLD_DOCKING_TIME);
            // 先通过订单生成时间过滤,订单生成时间早于旧单日期的一定是旧单
            List<Integer> appIdList = orderListRespVO.getOrderList().stream()
                    .filter(orderInfoVO -> DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, orderInfoVO.getOrder().getOrderDate()).after(huaNongOldDate))
                    .map(orderInfoVO -> orderInfoVO.getOrder().getBuyAppId()).collect(Collectors.toList());
            // 再去查采购单生成时间
            List<ApplicationMasterDTO> applicationMasterDTOList = applicationBaseClient.findByMasterId(appIdList);
            Map<Long, Date> appIdCreateTimeMap = DictionaryUtils.toMap(applicationMasterDTOList, ApplicationMasterDTO::getId, ApplicationMasterDTO::getCreateTime);
            // 如果采购单时间早于旧单时间，则为旧单。
            for (OrderInfoVO orderInfoVO : orderListRespVO.getOrderList()) {
                Integer buyAppId = orderInfoVO.getOrder().getBuyAppId();
                Date appCreateTime = buyAppId != null ? appIdCreateTimeMap.get(buyAppId.longValue()) : null;
                // 没有进行查的，都是订单生成时间早于旧单日期的，即采购生成时间早于旧单日期，所以一定是旧单
                if (appCreateTime != null) {
                    orderInfoVO.setOldFlag(appCreateTime.before(huaNongOldDate));
                } else {
                    // 兼容没有采购单id的旧数据，当它不是旧单。其余的都是旧单
                    orderInfoVO.setOldFlag(buyAppId != null);
                }
            }
        } else if(OrgEnum.QI_LU_GONG_YE_DA_XUE.getCode().equals(orgCode)){
            // 查询旧单时间配置
            OrgDockingConfigDTO config = dockingConfigCommonService.getConfig(orgCode);
            BusinessErrUtil.notNull(config, ExecptionMessageEnum.QILU_UNIVERSITY_CONFIG_NOT_READ);
            OldDateConfigDTO oldDateConfigDTO = config.getOldDateConfigDTO();

            // 判定旧单
            Date qiLuGongYeOldDate = oldDateConfigDTO.getOldDate();
            orderListRespVO.getOrderList().forEach(orderInfoVO -> {
                boolean beforeDateOrder = DateUtils
                        .parse(DateUtils.SIMPLE_DATE_FORMAT, orderInfoVO.getOrder().getOrderDate())
                        .before(qiLuGongYeOldDate);
                boolean unFreezed = !OrderFundStatusEnum.Freezed.getValue().equals(orderInfoVO.getOrder().getFundStatus());
                // 订单日期在旧单日期（上线日期）之前，且经费状态为未冻结的为旧单
                if (beforeDateOrder && unFreezed) {
                    orderInfoVO.setOldFlag(true);
                } else {
                    orderInfoVO.setOldFlag(false);
                }
            });
        } else if(OrgEnum.CHONG_QING_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN.getCode().equals(orgCode)){
            // 一分钱订单不对接财务，不标识成旧单
            orderListRespVO.getOrderList().forEach(item->item.setOldFlag(item.getOldFlag() && !item.getOrder().getTrialOrder()));
        }
    }

    /**
     * 获取需要进行查询的订单额外信息
     * @param orgId 单位id
     * @return 需查询的额外信息key
     */
    private List<Integer> getQueryOrderExtraKeyList(Integer orgId){
        List<Integer> queryExtraKeyList = New.list(
                OrderExtraEnum.IS_TRIAL_ORDER.getValue(),
                OrderExtraEnum.ORDER_ATTR.getValue(),
                OrderExtraEnum.ACCEPT_APPROVE_USERS.getValue(),
                OrderExtraEnum.REMARK.getValue(),
                OrderExtraEnum.STATEMENT_WAY_ID.getValue(),
                OrderExtraEnum.OUTER_SUPP_STATUS.getValue(),
                OrderExtraEnum.LACK_OF_GOODS.getValue(),
                OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue(),
                OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue(),
                OrderExtraEnum.ACCEPTANCE_WAY.getValue(),
                OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue(),
                OrderExtraEnum.ORDER_LIST_DOCKING_ERROR_HINT.getValue(),
                OrderExtraEnum.ORDER_TAG.getValue()
        );
        if(selfDefinedCategoryOrgIdList.contains(orgId)){
            // 农科院自定义的订单关联的分类
            queryExtraKeyList.add(OrderExtraEnum.SELF_DEFINED_CATEGORY.getValue());
        }
        if(needPurchaseTypeOrgIdList.contains(orgId)){
            // 农科院自定义的采购类型
            queryExtraKeyList.add(OrderExtraEnum.PURCHASE_TYPE.getValue());
        }
        if(OrgEnum.JI_NAN_DA_XUE.getValue() == orgId){
            queryExtraKeyList.add(OrderExtraEnum.REPORT_EXPENSE_STATUS.getValue());
        }
        return queryExtraKeyList;
    }

    private Map<Integer, List<OrderFundcardVO>> getOrderIdFundCardVOMap(String orgCode, List<OrderMasterSearchDTO> masterSearchList){
        List<String> cardIdList = masterSearchList.stream().map(OrderMasterSearchDTO::getCard).flatMap(List::stream).map(FundCardSearchDTO::getFundCardId).distinct().collect(toList());
        if (CollectionUtils.isEmpty(cardIdList)) {
            return New.emptyMap();
        }
        Map<Integer, List<OrderFundcardVO>> orderFundcardVOMap = New.mapWithCapacity(cardIdList.size());
        List<FundCardDTO> fullLevelTreeCards = researchFundCardServiceClient.getFundCardListByCardIds(cardIdList, orgCode);
        Map<String, FundCardDTO> fullLevelCardIdCardMap = FundCardUtils.getAllLevelCardIdCardMap(fullLevelTreeCards);
        Map<Integer, String> fundTypeMap = researchFundCardServiceClient.getFundTypeMap(orgCode);

        for(OrderMasterSearchDTO masterSearch : masterSearchList){
            if(CollectionUtils.isNotEmpty(masterSearch.getCard())){
                List<OrderFundcardVO> fundcardVOList = masterSearch.getCard().stream().map(cardItem->{
                    OrderFundcardVO orderFundcardVO = new OrderFundcardVO();
                    orderFundcardVO.setOrderId(masterSearch.getId());
                    orderFundcardVO.setCardId(cardItem.getFundCardId());
                    // campusName只有中国科学技术大学附属第一医院（安徽省立医院） 用到
                    orderFundcardVO.setCampusName(cardItem.getCampusName());

                    FundCardDTO fundCardDTO = fullLevelCardIdCardMap.get(cardItem.getFundCardId());
                    if(fundCardDTO != null){
                        Integer fundType = fundCardDTO.getFundType();
                        String fundTypeName = fundTypeMap.get(fundType);
                        orderFundcardVO.setFundType(fundType.toString());
                        orderFundcardVO.setFundTypeName(fundTypeName);
                        FundCardDTO firstLevelCard = FundCardUtils.getFirstLevelCard(fundCardDTO, fullLevelCardIdCardMap);
                        orderFundcardVO.setFirstLevelCardNo(firstLevelCard.getCode());
                        if(firstLevelCard.getFundCardManagerDTOs() != null){
                            orderFundcardVO.setFundManagerUserIds(firstLevelCard.getFundCardManagerDTOs().stream().map(FundCardManagerDTO::getUserId).collect(toList()));
                        }
                    }
                    return orderFundcardVO;
                }).collect(toList());
                orderFundcardVOMap.put(masterSearch.getId(), fundcardVOList);
            }
        }
        return orderFundcardVOMap;
    }

    /**
     * 验收审批提醒构建，取的是order_extra的ACCEPT_APPROVE_USERS项
     * @param order 订单
     * @param orderListRequireInfo 缓存
     */
    private void constructAcceptApprovalHint(OrderMasterVO order, OrderListRequireInfoBO orderListRequireInfo){
        if(OrderStatusEnum.OrderReceiveApproval.getValue().equals(order.getStatus())){
            // 初始化，如果下面没有重新赋值就显示这个了
            order.setAcceptApprovalHint("暂无审批人");
            Map<Integer, String> userIdNameMap = orderListRequireInfo.getUserIdNameMap();
            Map<Integer, List<OrderExtraDTO>> orderIdExtraMap = orderListRequireInfo.getOrderIdExtraMap();
            List<OrderExtraDTO> orderExtraDTOList = orderIdExtraMap.get(order.getId());
            String approvalAuthManStr = orderExtraDTOList.stream().filter(orderExtraDTO -> OrderExtraEnum.ACCEPT_APPROVE_USERS.getValue().equals(orderExtraDTO.getExtraKey())).findFirst().map(OrderExtraDTO::getExtraValue).orElse(null);
            if(StringUtils.isNotBlank(approvalAuthManStr) && !"[]".equals(approvalAuthManStr)){
                String[] manIdArr = approvalAuthManStr.substring(approvalAuthManStr.indexOf("[") + 1, approvalAuthManStr.indexOf("]")).split(",");
                if(manIdArr.length == 1 && StringUtils.isBlank(manIdArr[0])){
                    // 如果是空数组,不返回了
                    return;
                }
                // 产品限制了最大展示5个
                int maxCount = 5;
                List<Integer> manIdList = New.list();
                // 如果有没有缓存过的数据，代表需要查询
                boolean withNotQryMan = false;
                for(int i = 0 ; i < Math.min(maxCount, manIdArr.length); i++){
                    int manId = Integer.parseInt(manIdArr[i].trim());
                    manIdList.add(manId);
                    if(userIdNameMap.get(manId) == null){
                        withNotQryMan = true;
                    }
                }
                if(withNotQryMan){
                    List<UserBaseInfoDTO> userBaseInfoDTOList = userClient.getUserByIdsAndOrgId(manIdList, order.getOrgId());
                    userBaseInfoDTOList.forEach(user->userIdNameMap.put(user.getId(), user.getName()));
                }
                String manNameStr = manIdList.stream().map(userIdNameMap::get).filter(Objects::nonNull).collect(joining(","));
                order.setAcceptApprovalHint(String.format("待%s审批", manNameStr));
            }
        }
    }

    /**
     * 获取测试单数量
     * @param orgId 单位id
     * @param param 搜索参数
     * @return 测试单数量
     */
    private Long getTestCount(Integer orgId, OrderSearchParamDTO param){
        param.setPageSize(0);
        param.setStartHit(0);
        List<Integer> testUserIdList = userClient.queryTestUserByOrgId(orgId);
        Request request = orderSearchBoostService.searchRequestTransform(param);
        TermFilter testSuppFilter = new TermFilter("fsuppid", New.list(655, 58094, 60780));
        if(CollectionUtils.isNotEmpty(testUserIdList)){
            request.addFilter(new OrFilter()
                    .addFilter(new TermFilter("fbuyerid", testUserIdList))
                    .addFilter(testSuppFilter));
        }else {
            request.addFilter(testSuppFilter);
        }
        return orderSearchBoostService.search(request).getTotalHits();
    }

    /**
     * 获取验收审批原因映射
     */
    private Map<Integer, String> getWarehouseRejectReasonMap(Integer orgId, List<OrderMasterSearchDTO> masterSearchList){
        if(orgId != OrgEnum.GUANG_XI_ZHONG_LIU.getValue()){
            return New.emptyMap();
        }
        List<Integer> inventoryFailOrderIds = masterSearchList.stream().filter(item->InventoryStatusEnum.FAILED.getCode().equals(item.getInventoryStatus())).map(OrderMasterSearchDTO::getId).collect(toList());
        if(CollectionUtils.isEmpty(inventoryFailOrderIds)){
            return New.emptyMap();
        }
        List<OrderApprovalLog> logs = orderApprovalLogMapper.findByOrderIdInAndApproveStatusIn(inventoryFailOrderIds, New.list(OrderApprovalEnum.IN_BOUND_CALLBACK.getValue()));
        Map<Integer, OrderApprovalLog> orderIdLatestLogMap = DictionaryUtils.toMap(logs, OrderApprovalLog::getOrderId, Function.identity(), (o, n)->n.getId() >o.getId() ? n :o);
        Map<Integer, String> orderIdLatestLogReasonMap = New.mapWithCapacity(orderIdLatestLogMap.size());
        orderIdLatestLogMap.forEach((key, value) -> orderIdLatestLogReasonMap.put(key, value.getReason()));
        return orderIdLatestLogReasonMap;
    }

    /**
     * 构建 type-OrderDetailExtraDTO 映射
     *
     * @return key extraType - value OrderDetailExtraDTO 订单详情扩展信息
     */
    private Map<Integer, OrderDetailExtraDTO> getType2DetailExtraMap(List<OrderDetailExtraDTO> orderDetailExtraDTOList) {
        if (CollectionUtils.isEmpty(orderDetailExtraDTOList)) {
            return New.emptyMap();
        }
        Map<Integer, OrderDetailExtraDTO> type2DetailExtraMap = New.map();
        for (OrderDetailExtraDTO orderDetailExtraDTO : orderDetailExtraDTOList) {
            type2DetailExtraMap.put(orderDetailExtraDTO.getExtraKeyType(), orderDetailExtraDTO);
        }
        return type2DetailExtraMap;
    }
    /**
     * 获取订单上传文件信息
     *
     * @param orgId 单位ID
     * @param orderIdList 订单ID列表
     * @return 订单上传文件信息，结构为：订单ID -> (文件业务类型 -> 文件列表)
     */
    private Map<Integer, Map<Integer, List<OrderUploadFileDTO>>> getOrderIdUploadFileMap(Integer orgId, List<Integer> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return New.emptyMap();
        }

        // 需要查询的文件业务类型
        List<Integer> fileBusinessTypes = New.list(
                FileBusinessTypeEnum.ACCEPTANCE_ATTACHMENT.getCode(),
                FileBusinessTypeEnum.ACCEPTANCE_VIDEO.getCode()
        );

        // 查询支付记录 暂时只有西南医科大有
        if (Objects.equals(OrgEnum.XI_NAN_YI_KE_DA_XUE.getValue(), orgId)) {
            fileBusinessTypes.add(FileBusinessTypeEnum.PAYMENT_RECORD.getCode());
        }

        // 查询订单上传文件
        List<OrderUploadFileDTO> orderUploadFileList = orderUploadFileRpcClient.getOrderUploadFileList(orderIdList, fileBusinessTypes);
        if (CollectionUtils.isEmpty(orderUploadFileList)) {
            return New.emptyMap();
        }

        // 按订单ID和文件业务类型分组
        Map<Integer, Map<Integer, List<OrderUploadFileDTO>>> result = New.map();

        for (OrderUploadFileDTO fileDTO : orderUploadFileList) {
            Integer orderId = fileDTO.getOrderId();
            Integer fileBusinessType = fileDTO.getFileBusinessType();

            // 第一层Map：订单ID -> 文件类型Map
            Map<Integer, List<OrderUploadFileDTO>> typeToFilesMap = result.get(orderId);
            if (Objects.isNull(typeToFilesMap)) {
                typeToFilesMap = New.map();
                result.put(orderId, typeToFilesMap);
            }

            // 第二层Map：文件类型 -> 文件列表
            List<OrderUploadFileDTO> fileList = typeToFilesMap.get(fileBusinessType);
            if (Objects.isNull(fileList)) {
                fileList = New.list();
                typeToFilesMap.put(fileBusinessType, fileList);
            }

            // 将当前文件添加到对应类型的列表中
            fileList.add(fileDTO);
        }

        return result;
    }


    /**
     * 查询线下单收款账户类型
     *
     * @param orderMasterList 订单主表数据集合
     * @param orgId 组织ID
     * @return 订单ID到账户类型数据的映射
     */
    public Map<Integer, Integer> getOrderAccountTypeMap(List<OrderMasterSearchDTO> orderMasterList, Integer orgId) {
        // 暂时只有西南医科大用
        if (!Objects.equals(OrgEnum.XI_NAN_YI_KE_DA_XUE.getValue(), orgId)) {
            return New.emptyMap();
        }

        if (CollectionUtils.isEmpty(orderMasterList)) {
            return New.emptyMap();
        }

        // 过滤出线下单ID
        List<Integer> offlineOrderIds = orderMasterList.stream()
                .filter(order -> Objects.equals(ProcessSpeciesEnum.OFFLINE.getValue(), order.getSpecies()))
                .map(OrderMasterSearchDTO::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(offlineOrderIds)) {
            return New.emptyMap();
        }

        // 查询订单银行数据
        List<OrderBankDataDTO> orderBankDataList = orderBankSnapshotClient.listByOrderId(offlineOrderIds);
        if (CollectionUtils.isEmpty(orderBankDataList)) {
            return New.emptyMap();
        }

        // 构建orderId到accountType的映射
        Map<Integer, Integer> resultMap = New.map();
        for (OrderBankDataDTO bankData : orderBankDataList) {
            resultMap.put(bankData.getOrderId(), bankData.getAccountType());
        }

        return resultMap;
    }

    /**
     * 设置特殊的列表tag
     * @param orgId 单位id
     * @param orderListRespVO 列表数据
     */
    private void setCustomTag(Integer orgId, OrderListRespVO orderListRespVO){
        if(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getValue() == orgId){
            for(OrderInfoVO order : orderListRespVO.getOrderList()){
                // 非对接单定义：非特殊的服务类单据/非对接卡/Q开头的卡
                boolean notDockingOrder = order.getOrderDetails().stream()
                        .anyMatch(d -> {
                            boolean isService = CategoryConstant.SCIENCE_SERVICE_ID.equals(d.getFirstCategoryId());
                            boolean isSpecialService = CategoryConstant.BIO_SCI_SERVICE_ID.equals(d.getSecondCategoryId())
                                    || CategoryConstant.CHEMICAL_TECH_SERVICE_ID.equals(d.getSecondCategoryId())
                                    || CategoryConstant.ANIMAL_EXPERIMENT_SERVICE_ID.equals(d.getSecondCategoryId());
                            return isService && !isSpecialService;
                        });
                if(!notDockingOrder){
                    if(order.getOrder().getFundCard() != null){
                        notDockingOrder = String.valueOf(FundTypeEnum.NOT_FINANCIAL.getValue()).equals(order.getOrder().getFundCard().getFundType())
                                || order.getOrder().getFundCard().getFirstLevelCardNo().startsWith("Q");
                    }
                }
                if(notDockingOrder){
                    order.getShowTagList().add(OrderListTagEnum.NOT_DOCKING_ORDER.getTagName());
                }
            }
        }
    }
}
