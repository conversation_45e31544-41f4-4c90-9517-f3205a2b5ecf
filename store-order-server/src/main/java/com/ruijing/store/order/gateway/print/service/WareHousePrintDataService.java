package com.ruijing.store.order.gateway.print.service;

import com.ruijing.store.order.gateway.print.dto.warehouse.WarehousePrintDataDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/28 16:50
 * @description
 */
public interface WareHousePrintDataService {

    /**
     * 获取打印数据
     * @param orderNoList 订单idList
     * @return 打印数据
     */
    List<WarehousePrintDataDTO> getPrintData(List<String> orderNoList);
}
