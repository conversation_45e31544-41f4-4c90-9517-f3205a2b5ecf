package com.ruijing.store.order.gateway.print.service.impl;

import com.reagent.bid.api.rpc.enums.OperationEnum;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardManagerDTO;
import com.reagent.research.statement.api.enums.InvoiceTypeEnum;
import com.reagent.research.statement.api.invoice.dto.InvoiceDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceQueryDTO;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderApprovalRequestDTO;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.api.base.other.dto.OrderPurchaseApprovalLogDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.SuppShopInfoBO;
import com.ruijing.store.order.business.enums.myorderlist.OrderSpeciesEnum;
import com.ruijing.store.order.gateway.print.dto.ApprovalLogFlatListDTO;
import com.ruijing.store.order.gateway.print.dto.order.OrderPrintApprovalLogDTO;
import com.ruijing.store.order.gateway.print.dto.other.BatchesPrintDTO;
import com.ruijing.store.order.gateway.print.dto.warehouse.*;
import com.ruijing.store.order.gateway.print.service.WareHousePrintDataService;
import com.ruijing.store.order.gateway.print.util.ApprovalLogTranslator;
import com.ruijing.store.order.gateway.print.warehouse.InWarehousePrintDataService;
import com.ruijing.store.order.gateway.print.warehouse.OutWarehousePrintDataService;
import com.ruijing.store.order.gateway.print.warehouse.WarehouseClaimPrintDataService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.service.OrderBatchService;
import com.ruijing.store.order.util.QrCodeUtil;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2023/2/28 16:50
 * @description
 */
@Service
public class WareHousePrintDataServiceImpl implements WareHousePrintDataService {

    @PearlValue(key = "wechat.order.detail.url")
    private String WECHAT_ORDER_DETAIL_URL;
    
    @Resource
    private InWarehousePrintDataService inWarehousePrintDataService;
    
    @Resource
    private OutWarehousePrintDataService outWarehousePrintDataService;
    
    @Resource
    private OrderApprovalLogService orderApprovalLogService;
    
    @Resource
    private OrderMasterMapper orderMasterMapper;
    
    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private GetPrintCommonDataService getPrintCommonDataService;
    
    @Resource
    private InvoiceClient invoiceClient;

    @Resource
    private UserClient userClient;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private OrganizationClient organizationClient;

    @Resource
    private DepartmentRpcClient departmentRpcClient;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private SuppClient suppClient;

    @Resource
    private WarehouseClaimPrintDataService warehouseClaimPrintDataService;

    @Resource
    private OrderBatchService orderBatchService;

    @Override
    public List<WarehousePrintDataDTO> getPrintData(List<String> orderNoList){
        Preconditions.notEmpty(orderNoList, "入参不可为空");
        List<OrderMasterDO> allOrderMasterDOList = orderMasterMapper.findByFordernoIn(orderNoList);
        if(CollectionUtils.isEmpty(allOrderMasterDOList)){
            return New.emptyList();
        }
        // 获取入库数据
        List<BizWarehouseEntryDTO> allBizWarehouseEntryDTOList = inWarehousePrintDataService.getBizWarehouseEntryDtoListByOrderNo(orderNoList);
        if(CollectionUtils.isEmpty(allBizWarehouseEntryDTOList)){
            return New.emptyList();
        }
        Map<String, List<BizWarehouseEntryDTO>> orderNoBizEntryListMap = DictionaryUtils.groupBy(allBizWarehouseEntryDTOList, BizWarehouseEntryDTO::getOrderNo);

        // 过滤掉没有入库单的数据
        allOrderMasterDOList = allOrderMasterDOList.stream().filter(orderMasterDO -> orderNoBizEntryListMap.get(orderMasterDO.getForderno()) != null).collect(toList());
        // 取订单商品数据
        List<Integer> allOrderIdList = allOrderMasterDOList.stream().map(OrderMasterDO::getId).collect(toList());
        List<OrderDetailDO> allOrderDetailDOList = orderDetailMapper.findAllByFmasteridIn(allOrderIdList);
        Map<Integer, List<OrderDetailDO>> orderIdDetailMap = DictionaryUtils.groupBy(allOrderDetailDOList, OrderDetailDO::getFmasterid);

        List<Integer> allAppIdList = allOrderMasterDOList.stream().filter(orderMasterDO -> OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderMasterDO.getOrderType())).map(OrderMasterDO::getFtbuyappid).distinct().collect(toList());;
        List<ApplicationMasterDTO> applicationMasterDTOList = applicationBaseClient.findByMasterId(allAppIdList);
        Map<Long, ApplicationMasterDTO> appIdIdentityMap = DictionaryUtils.toMap(applicationMasterDTOList, ApplicationMasterDTO::getId, Function.identity());
        // 供应商联系电话（线上）
        Map<Integer, List<Integer>> orgIdSuppIdMap = allOrderMasterDOList.stream().filter(orderMasterDO -> OrderSpeciesEnum.NORMAL.getValue().byteValue() == orderMasterDO.getSpecies()).collect(Collectors.groupingBy(OrderMasterDO::getFuserid, Collectors.mapping(OrderMasterDO::getFsuppid, Collectors.toList())));
        Map<Integer, Map<Integer, SuppShopInfoBO>> orgIdSuppIdContactMap = New.map();
        for(Map.Entry<Integer, List<Integer>> entry : orgIdSuppIdMap.entrySet()){
            Map<Integer, SuppShopInfoBO> suppIdSuppMap = suppClient.getSupplierContactInfoMap(entry.getValue(), entry.getKey());
            orgIdSuppIdContactMap.put(entry.getKey(), suppIdSuppMap);
        }
        List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOList = orderBatchService.getBatchesByOrders(New.list(allOrderMasterDOList));
        Map<Integer, List<BatchesPrintDTO>> detailIdBatchesMap = New.map();
        for(OrderUniqueBarCodeDTO item : orderUniqueBarCodeDTOList){
            BatchesPrintDTO batchesPrintDTO = new BatchesPrintDTO();
            batchesPrintDTO.setBatches(item.getBatches());
            batchesPrintDTO.setExpiration(item.getExpiration());
            batchesPrintDTO.setManufacturer(item.getManufacturer());
            batchesPrintDTO.setProductionDate(item.getProductionDate());
            batchesPrintDTO.setQuantity(item.getTotal());
            detailIdBatchesMap.computeIfAbsent(item.getOrderDetailId(), (k)->New.list()).add(batchesPrintDTO);
        }

        List<WarehousePrintDataDTO> warehousePrintDataDTOList = new ArrayList<>(orderNoBizEntryListMap.size());
        for(OrderMasterDO orderMasterDO : allOrderMasterDOList){
            List<BizWarehouseEntryDTO> bizWarehouseEntryDTOList = orderNoBizEntryListMap.get(orderMasterDO.getForderno());
            if(CollectionUtils.isEmpty(bizWarehouseEntryDTOList)){
                continue;
            }
            WarehousePrintDataDTO warehousePrintDataDTO = new WarehousePrintDataDTO();
            List<OrderDetailDO> orderDetailDOList = orderIdDetailMap.get(orderMasterDO.getId());
            List<InWarehousePrintDataDTO> inWarehousePrintDataDTOList = inWarehousePrintDataService.getInWarehousePrintData(orderMasterDO, orderDetailDOList, bizWarehouseEntryDTOList);
            List<OutWarehousePrintDataDTO> outWarehousePrintDataDTOList = outWarehousePrintDataService.getOutWarehousePrintData(orderMasterDO, orderDetailDOList, bizWarehouseEntryDTOList);
            List<WarehouseClaimPrintDataDTO> warehouseClaimPrintDataDTOList = warehouseClaimPrintDataService.getClaimPrintData(orderMasterDO, orderDetailDOList, bizWarehouseEntryDTOList);
            inWarehousePrintDataDTOList.forEach(inWarehousePrintDataDTO -> {
                inWarehousePrintDataDTO.setOrderNo(orderMasterDO.getForderno());
                inWarehousePrintDataDTO.setOrderSpecies(orderMasterDO.getSpecies().intValue());
            });
            warehousePrintDataDTO.setInWarehouseDataList(inWarehousePrintDataDTOList);
            outWarehousePrintDataDTOList.forEach(outWarehousePrintDataDTO -> {
                outWarehousePrintDataDTO.setOrderNo(orderMasterDO.getForderno());
                outWarehousePrintDataDTO.setOrderSpecies(orderMasterDO.getSpecies().intValue());
            });
            warehousePrintDataDTO.setOutWarehouseDataList(outWarehousePrintDataDTOList);
            ApplicationMasterDTO appData = orderMasterDO.getFtbuyappid() == null ? null : appIdIdentityMap.get(orderMasterDO.getFtbuyappid().longValue());
            warehousePrintDataDTO.setOrderData(this.getOrderPrintData(orderMasterDO, appData));
            // 供应商联系电话（线上）
            Map<Integer, SuppShopInfoBO> suppIdSuppMap;
            SuppShopInfoBO suppShopInfoBO;
            if((suppIdSuppMap = orgIdSuppIdContactMap.get(orderMasterDO.getFuserid())) != null && (suppShopInfoBO = suppIdSuppMap.get(orderMasterDO.getFsuppid())) != null){
                warehousePrintDataDTO.getOrderData().setSupplierPhone(suppShopInfoBO.getTelephone());
            }
            warehousePrintDataDTO.setWarehouseReceiveDataList(warehouseClaimPrintDataDTOList);
            warehousePrintDataDTOList.add(warehousePrintDataDTO);

            // 有批次则-按批次维度来构建数据
            for(InWarehousePrintDataDTO inWarehousePrintDataDTO : inWarehousePrintDataDTOList){
                for(WarehouseProductPrintDataDTO productItem : inWarehousePrintDataDTO.getWarehouseProductInfoVOList()){
                    productItem.setBatchDataList(detailIdBatchesMap.get(productItem.getOrderDetailId()));
                }
            }
        }
        // 填充日志
        this.fillCommonLog(allOrderMasterDOList, warehousePrintDataDTOList);
        return warehousePrintDataDTOList;
    }

    /**
     * 装填订单数据到打印数据中
     * @param orderMasterDO 订单数据
     */
    private WareHouseOrderPrintDataDTO getOrderPrintData(OrderMasterDO orderMasterDO, ApplicationMasterDTO applicationMasterDTO) {
        WareHouseOrderPrintDataDTO printData = new WareHouseOrderPrintDataDTO();
        printData.setOrderNo(orderMasterDO.getForderno());
        //获取订单号对应的条形码
        printData.setOrderNoBarcode(getPrintCommonDataService.getBarCode(orderMasterDO.getForderno()));
        printData.setOrderNoQrCode(getPrintCommonDataService.getQrCode(orderMasterDO.getForderno()));
        printData.setOrderId(orderMasterDO.getId());
        printData.setOrderType(OrderTypeEnum.getByCode(orderMasterDO.getOrderType()));
        printData.setOrderTotalPrice(orderMasterDO.getForderamounttotal());
        //找采购人信息
        UserBaseInfoDTO purchaser = userClient.getNotNullUserDetailById(orderMasterDO.getFbuyerid());
        printData.setPurchaserName(purchaser.getName());
        printData.setPurchaserPhone(purchaser.getMobile());
        
        printData.setOrderSpecies(orderMasterDO.getSpecies().intValue());
        printData.setOrgId(orderMasterDO.getFuserid());
        printData.setOrgName(orderMasterDO.getFusername());
        printData.setSupplierCode(orderMasterDO.getFsuppcode());
        printData.setSupplierName(orderMasterDO.getFsuppname());
        //部门
        printData.setDepartmentName(orderMasterDO.getFbuydepartment());
        DepartmentDTO deptParentInfo = departmentRpcClient.getDepartmentParentInfo(orderMasterDO.getFbuydepartmentid());
        if(deptParentInfo != null){
            printData.setDepartmentParentName(deptParentInfo.getName());    
        }
        // 宁波二院定制，验收人强制为马海香
        String acceptor = OrgEnum.NING_BO_ER_YUAN.getCode().equals(orderMasterDO.getFusercode()) ? "马海香" : orderMasterDO.getFlastreceiveman();
        printData.setAcceptor(acceptor);
        printData.setOrderReceiptDate(orderMasterDO.getFlastreceivedate() == null ? null : orderMasterDO.getFlastreceivedate().getTime());
        
        List<String> receivePhotos = orderMasterDO.getReceivePicUrls() == null ? New.emptyList() : Arrays.asList(orderMasterDO.getReceivePicUrls().split(";"));
        printData.setReceivedPictures(receivePhotos);
        // 装填经费
        this.constructFundDataToPrintData(orderMasterDO, printData);
        //根据订单对应部门获取部门负责人
        printData.setDepartmentDirector(this.getDepartmentDirectorName(orderMasterDO.getFbuydepartmentid()));
        //获取订单对应的发票信息
        this.constructInvoiceToInWarehouseData(orderMasterDO.getId(), printData);

        // 获取单位logo
        OrganizationDTO organizationDTO = organizationClient.findByIdWithCache(orderMasterDO.getFuserid());
        // 再装填订单相关数据
        printData.setOrgLogo(Objects.nonNull(organizationDTO) ? organizationDTO.getTransparentLogo() : null);

        if(applicationMasterDTO != null){
            printData.setPurchaseNote(applicationMasterDTO.getApplyInfo());
        }
        try {
            // 生成微信订单详情二维码
            String wechatOrderDetailLink = String.format(WECHAT_ORDER_DETAIL_URL, orderMasterDO.getId());
            String wechatOrderDetailQrCode = QrCodeUtil.getBase64(wechatOrderDetailLink, 120, 120);
            printData.setWechatOrderDetailQrCode(wechatOrderDetailQrCode);
        } catch (Exception e) {
            Cat.logError(this.getClass().getSimpleName(), "getOrderPrintData", "生成微信订单详情二维码失败\n入参:" + orderMasterDO.getId(), e);
        }
        return printData;
    }

    /**
     * 发票数据装填
     * @param orderId 订单id
     * @param inWarehousePrintDataDTO 入库单数据
     */
    private void constructInvoiceToInWarehouseData(Integer orderId, WareHouseOrderPrintDataDTO inWarehousePrintDataDTO) {
        InvoiceQueryDTO queryDTO = new InvoiceQueryDTO();
        queryDTO.setSourceIds(Collections.singletonList(orderId.longValue()));
        queryDTO.setInvoiceType(InvoiceTypeEnum.ORDER);
        List<InvoiceDTO> invoiceDTOList = invoiceClient.findInvoiceList(queryDTO);
        inWarehousePrintDataDTO.setInvoiceNo(invoiceDTOList.stream().map(InvoiceDTO::getInvoiceNo).collect(Collectors.joining(";\n")));
        inWarehousePrintDataDTO.setInvoiceNoList(invoiceDTOList.stream().map(InvoiceDTO::getInvoiceNo).collect(Collectors.toList()));
        inWarehousePrintDataDTO.setInvoiceDateTimeList(invoiceDTOList.stream().map(invoiceDTO -> invoiceDTO.getInvoiceDate().getTime()).collect(Collectors.toList()));
    }

    /**
     * 经费数据状态
     * @param orderMasterDO 订单
     * @param inWarehousePrintDataDTO 打印数据
     */
    private void constructFundDataToPrintData(OrderMasterDO orderMasterDO, WareHouseOrderPrintDataDTO inWarehousePrintDataDTO) {
        inWarehousePrintDataDTO.setFunCardNo(StringUtils.EMPTY);
        inWarehousePrintDataDTO.setProjectCode(StringUtils.EMPTY);
        inWarehousePrintDataDTO.setProjectName(StringUtils.EMPTY);
        inWarehousePrintDataDTO.setFundTypeName(StringUtils.EMPTY);
        inWarehousePrintDataDTO.setFundCardManagerName(StringUtils.EMPTY);
        List<RefFundcardOrderDTO> refFundCardOrderDTOList = refFundcardOrderService.findByOrderId(New.list(orderMasterDO.getId().toString()));
        if (CollectionUtils.isNotEmpty(refFundCardOrderDTOList)) {
            inWarehousePrintDataDTO.setFunCardNo(refFundCardOrderDTOList.stream().map(RefFundcardOrderDTO::getCardNo).collect(Collectors.joining(",")));
            List<String> cardIdList = refFundCardOrderDTOList.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).collect(toList());
            //获取入库单对应的订单的经费项目的项目编码、项目名称、经费卡号
            List<FundCardDTO> fundCardDTOList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(orderMasterDO.getFusercode(), cardIdList);
            inWarehousePrintDataDTO.setProjectCode(fundCardDTOList.stream().map(FundCardDTO::getCode).collect(Collectors.joining(",")));
            inWarehousePrintDataDTO.setProjectName(fundCardDTOList.stream().map(FundCardDTO::getName).collect(Collectors.joining(",")));
            // 获取经费类型
            List<FundCardDTO> accurateFundCardList = researchFundCardServiceClient.getFundCardListByCardIds(cardIdList, orderMasterDO.getFusercode());
            List<Integer> fundTypeList = accurateFundCardList.stream().map(FundCardDTO::getFundType).collect(toList());
            Map<Integer, String> fundTypeMap = researchFundCardServiceClient.getFundTypeMap(orderMasterDO.getFusercode());
            if (CollectionUtils.isNotEmpty(fundTypeList)) {
                inWarehousePrintDataDTO.setFundTypeName(fundTypeList.stream().map(fundTypeMap::get).filter(Objects::nonNull).collect(Collectors.joining(",")));
            }

            // 设置经费负责人姓名
            inWarehousePrintDataDTO.setFundCardManagerName(fundCardDTOList .stream()
                    .flatMap(fundCardDTO -> Optional.ofNullable(fundCardDTO.getFundCardManagerDTOs())
                            .orElse(Collections.emptyList())
                            .stream())
                    .filter(Objects::nonNull)
                    .map(FundCardManagerDTO::getManagerName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(",")));

        }
    }


    private String getDepartmentDirectorName(Integer departmentId) {
        //根据订单对应部门获取部门负责人信息
        DepartmentDTO departmentDTO = userClient.getDepartmentInfo(departmentId);
        BusinessErrUtil.notNull(departmentDTO, ExecptionMessageEnum.DEPARTMENT_INFO_NOT_FOUND_FOR_ORDER, departmentId);
        if (departmentDTO.getManagerId() != null) {
            UserBaseInfoDTO departmentDirectorInfo = userClient.getNotNullUserDetailById(departmentDTO.getManagerId());
            return departmentDirectorInfo.getName();
        }
        return StringUtils.EMPTY;
    }
    
    private void fillCommonLog(List<OrderMasterDO> orderMasterDOList, List<WarehousePrintDataDTO> warehousePrintDataDTOList){
        // 获取验收审批日志
        Integer orgId = orderMasterDOList.get(0).getFuserid();
        List<Integer> orderIdList = orderMasterDOList.stream().map(OrderMasterDO::getId).collect(toList());
        Map<Integer, List<OrderApprovalLogDTO>> orderIdLogMap = this.getLastPassOrderIdApprovalLogMap(orderIdList);
        // 获取采购/竞价单日志
        Map<Integer, List<OrderPurchaseApprovalLogDTO>> orderIdPurchaseOrBidLog = getPrintCommonDataService.getPurchaseOrBidLog(orderMasterDOList, New.list(OperationEnum.BEGIN_APPROVAL_PASS.getName(), OperationEnum.FINAL_APPROVAL_PASS.getName()), true);
        Map<String, OrderMasterDO> orderNoMasterMap = DictionaryUtils.toMap(orderMasterDOList, OrderMasterDO::getForderno, Function.identity());
        for(WarehousePrintDataDTO warehousePrintDataDTO : warehousePrintDataDTOList){
            OrderMasterDO orderMasterDO = orderNoMasterMap.get(warehousePrintDataDTO.getOrderData().getOrderNo());
            // 采购单日志
            ApprovalLogFlatListDTO purchaseLogFlatList = null;
            // 竞价单初审日志
            ApprovalLogFlatListDTO bidInitialLogFlatList = null;
            // 竞价单终审日志
            ApprovalLogFlatListDTO bidFinalLogFlatList = null;
            if (OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderMasterDO.getOrderType())) {
                List<OrderPurchaseApprovalLogDTO> purchaseLogList = orderIdPurchaseOrBidLog.getOrDefault(orderMasterDO.getId(), New.emptyList());
                purchaseLogFlatList = ApprovalLogTranslator.orderPurchaseApprovalLog2FlatListDTO(purchaseLogList);
            } else if (OrderTypeEnum.BID_ORDER.getCode().equals(orderMasterDO.getOrderType())) {
                // 初审
                List<OrderPurchaseApprovalLogDTO> bidInitialPurchaseLog = orderIdPurchaseOrBidLog.getOrDefault(orderMasterDO.getId(), New.emptyList()).stream().filter(log -> OperationEnum.BEGIN_APPROVAL_PASS.getName().equals(log.getOperate())).collect(toList());
                bidInitialLogFlatList = ApprovalLogTranslator.orderPurchaseApprovalLog2FlatListDTO(bidInitialPurchaseLog);
                // 终审
                List<OrderPurchaseApprovalLogDTO> bidFinalPurchaseLog = orderIdPurchaseOrBidLog.getOrDefault(orderMasterDO.getId(), New.emptyList()).stream().filter(log -> OperationEnum.FINAL_APPROVAL_PASS.getName().equals(log.getOperate())).collect(toList());
                bidFinalLogFlatList = ApprovalLogTranslator.orderPurchaseApprovalLog2FlatListDTO(bidFinalPurchaseLog);
            }
            warehousePrintDataDTO.setPurchaseLog(purchaseLogFlatList);
            warehousePrintDataDTO.setBidInitialApproveLog(bidInitialLogFlatList);
            warehousePrintDataDTO.setBidFinalApproveLog(bidFinalLogFlatList);
            List<OrderPrintApprovalLogDTO> purchaseOrBidApprovalLogList = orderIdPurchaseOrBidLog.getOrDefault(orderMasterDO.getId(), New.emptyList()).stream().map(ApprovalLogTranslator::orderPurchaseApprovalLog2PrintLog).collect(toList());
            warehousePrintDataDTO.setPurchaseOrBidApprovalLogList(purchaseOrBidApprovalLogList);
            warehousePrintDataDTO.setSubmitApprovalLog(ApprovalLogTranslator.getLastSubmitApprovalLog(orderIdPurchaseOrBidLog.getOrDefault(orderMasterDO.getId(), New.emptyList())));
            warehousePrintDataDTO.setOrderApproveSuccessLog(ApprovalLogTranslator.orderAcceptApproveLog2FlatListDTO(orderIdLogMap.get(orderMasterDO.getId())));
            OrderApprovalLogDTO acceptLog = orderIdLogMap.get(orderMasterDO.getId()).stream().filter(log -> OrderApprovalEnum.RECEIPT.getValue().equals(log.getApproveStatus())).findFirst().orElse(null);
            warehousePrintDataDTO.setSubmitAcceptLog(ApprovalLogTranslator.acceptApproveLog2PrintLog(acceptLog));
        }
    }

    /**
     * 获取订单id-验收审批日志映射
     *
     * @param orderIdList 订单id列表
     * @return 映射
     */
    private Map<Integer, List<OrderApprovalLogDTO>> getLastPassOrderIdApprovalLogMap(List<Integer> orderIdList) {
        // 获取验收/验收审批日志
        List<OrderApprovalLogDTO> orderApprovalLogList;
        OrderApprovalRequestDTO orderApprovalRequestDTO = new OrderApprovalRequestDTO();
        orderApprovalRequestDTO.setOrderIdList(orderIdList);
        orderApprovalRequestDTO.setTypeList(New.list(OrderApprovalEnum.RECEIPT.getValue(), OrderApprovalEnum.PASS.getValue(), OrderApprovalEnum.REJECT.getValue()));
        // 获取最后一手有效的验收审批日志
        orderApprovalLogList = orderApprovalLogService.getLastPassAcceptApproveLog(orderApprovalRequestDTO);
        // 获取电子签名
        orderApprovalLogService.fillLogWithElectronicSign(orderApprovalLogList, New.list(ElectronicSignatureOperationEnum.ORDER_RECEIVE, ElectronicSignatureOperationEnum.ACCEPTANCE_APPROVAL));
        return DictionaryUtils.groupBy(orderApprovalLogList, OrderApprovalLogDTO::getOrderId);

    }
}
