package com.ruijing.order.cache;

import com.ruijing.fundamental.cache.api.CacheKey;
import com.ruijing.fundamental.cache.redis.client.RedisCacheClient;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.fundamental.remoting.msharp.constant.ProtocolConstant;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import java.util.Optional;

@ServiceClient
@ConditionalOnProperty(name = "order.extension.simpleRedisClient")
public class RedisClient {

    private final static String SERVICE_NAME = "store-order";

    @MSharpReference(remoteAppkey = "msharp-cache-service", protocol = ProtocolConstant.REDIS)
    private RedisCacheClient redisCacheClient;


    /**
     * 原子操作, 分布式锁
     * @param uniqKey   唯一key
     * @param timeLimit 过期时间 second
     */
    @ServiceLog(description = "分布式锁，防重", serviceType = ServiceType.RPC_CLIENT)
    public void getLock(String uniqKey, Integer timeLimit) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        Boolean notInCache = redisCacheClient.setIfAbsent(cacheKey, Environment.getAppKey() + ":" + Thread.currentThread().getId(), timeLimit);
        Preconditions.isTrue(notInCache, "操作过于频繁,请稍后再试");
    }

    /**
     * @description: 缓存数据到redis
     * @date: 2021/2/25 12:59
     * @author: zengyanru
     * @param uniqKey
     * @param value
     * @param timeLimit
     * @return void
     */
    @ServiceLog(description = "缓存数据到redis", serviceType = ServiceType.RPC_CLIENT)
    public void putToCache(String uniqKey, Object value, Integer timeLimit) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        redisCacheClient.setIfAbsent(cacheKey, value, timeLimit);
    }

    /**
     * @description: 从缓存获取对象
     * @date: 2021/2/25 13:05
     * @author: zengyanru
     * @param uniqKey
     * @return java.lang.Object
     */
    @ServiceLog(description = "从缓存获取对象", serviceType = ServiceType.RPC_CLIENT)
    public Optional<Object> getFromCache(String uniqKey) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        return Optional.ofNullable(redisCacheClient.get(cacheKey));
    }

    /**
     * @description: 手动删除缓存
     * @date: 2021/2/25 13:10
     * @author: zengyanru
     * @param uniqKey
     */
    @ServiceLog(description = "操作完成后立马删除对应的操作缓存，避免多余的等待时间", serviceType = ServiceType.RPC_CLIENT)
    public void removeCache(String uniqKey) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        redisCacheClient.delete(cacheKey);
    }

    /**
     * 原子操作, 释放锁
     *
     * @param uniqKey 唯一key
     */
    @ServiceLog(description = "操作完成后立马删除对应的操作缓存，避免多余的等待时间", serviceType = ServiceType.RPC_CLIENT)
    public void unlock(String uniqKey) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        redisCacheClient.compareAndDelete(cacheKey, Environment.getAppKey() + ":" + Thread.currentThread().getId());
    }

    /**
     * 原子操作, 分布式锁
     *
     * @param uniqKey   唯一key
     * @param timeLimit 过期时间 second
     * @return 是否成功
     */
    @ServiceLog(description = "分布式锁，防重", serviceType = ServiceType.RPC_CLIENT)
    public boolean tryLock(String uniqKey, Integer timeLimit) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        return redisCacheClient.setIfAbsent(cacheKey, Environment.getAppKey() + ":" + Thread.currentThread().getId(), timeLimit);
    }

    /**
     * 阻塞式获取分布式锁(可重入)
     *
     * @param uniqKey 唯一key
     * @param lockTimeLimit 锁过期时间 second
     * @param waitTimeLimit 等待获取锁时间 second
     * @return 是否成功获取锁
     */
    @ServiceLog(description = "阻塞式分布式锁，防重", serviceType = ServiceType.RPC_CLIENT)
    public boolean lockWithWait(String uniqKey, Integer lockTimeLimit, Integer waitTimeLimit) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        return redisCacheClient.tryLock(cacheKey, waitTimeLimit * 1000L, lockTimeLimit * 1000L);
    }

    /**
     * 释放阻塞式分布式锁
     *
     * @param uniqKey 唯一key
     */
    @ServiceLog(description = "释放阻塞式分布式锁", serviceType = ServiceType.RPC_CLIENT)
    public boolean unlockWithWait(String uniqKey) {
        CacheKey cacheKey = new CacheKey(SERVICE_NAME, uniqKey);
        return redisCacheClient.unLock(cacheKey);
    }
}
